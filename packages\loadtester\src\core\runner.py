"""
Enhanced Asyncio Test Runner
===========================

Main orchestrator for running different types of load tests using asyncio.
Integrates test types, monitoring, and the existing sequence execution logic.
"""

import asyncio
import time
import random
import traceback
from typing import Dict, Any, List

from ..core.processor import TestSequence
from ..core.timer import Timer
from ..core.clients import HttpClient
from .loads import TestType, create_test_type, WorkerConfig
from .monitoring import AsyncioMonitor, RequestMetric


class EnhancedTestSequence(TestSequence):
   """Enhanced test sequence with monitoring and test type support"""
   
   def __init__(self, config: Dict[str, Any], monitor: AsyncioMonitor, 
                worker_config: WorkerConfig, test_type: TestType):
       # Use a dummy timer since we handle timing in the monitor
       dummy_timer = Timer()
       super().__init__(config, dummy_timer)
       
       self.monitor = monitor
       self.worker_config = worker_config
       self.test_type = test_type
       self.should_stop = False
       
   async def run_sequence_with_monitoring(self) -> bool:
       """Run sequence with enhanced monitoring and test type behaviors"""
       try:
           # Register worker start
           self.monitor.start_worker(self.worker_config.worker_id)
           
           # Wait for delayed start if configured
           if self.worker_config.delay_start > 0:
               await asyncio.sleep(self.worker_config.delay_start)
               
           # Get modified endpoint sequence for this worker
           original_endpoints = self.config.get("endpoints", [])
           modified_endpoints = self.test_type.modify_sequence_for_worker(
               original_endpoints, self.worker_config
           )
           
           # Create config with modified endpoints
           worker_config = self.config.copy()
           worker_config["endpoints"] = modified_endpoints
           
           # Create HTTP client
           verify_ssl = worker_config.get("verify_ssl", True)
           client = HttpClient(
               worker_config["base_url"],
               worker_config["jwt_token"],
               self.timer,
               verify_ssl=verify_ssl,
               config=worker_config
           )
           
           iteration = 0
           while not self.should_stop:
               iteration += 1
               sequence_start_time = time.time()
               
               # Execute the sequence
               success = await self._execute_monitored_sequence(
                   client, worker_config, iteration
               )
               
               if not success:
                   break
                   
               # Think time between iterations
               think_time = random.uniform(
                   self.worker_config.think_time_min,
                   self.worker_config.think_time_max
               )
               await asyncio.sleep(think_time)
               
           # Register worker end
           self.monitor.end_worker(self.worker_config.worker_id)
           return True
           
       except Exception as e:
           print(f"Worker {self.worker_config.worker_id} failed: {str(e)}")
           traceback.print_exc()
           self.monitor.end_worker(self.worker_config.worker_id)
           return False
           
   def stop(self):
       """Signal the worker to stop"""
       self.should_stop = True
       
   async def _execute_monitored_sequence(self, client: HttpClient, 
                                       config: Dict[str, Any], iteration: int) -> bool:
       """Execute sequence with detailed monitoring"""
       try:
           endpoints = config.get("endpoints", [])
           
           for step, endpoint_config in enumerate(endpoints, 1):
               if self.should_stop:
                   return False
                   
               # Check for chaos injection
               if self.test_type.inject_chaos(self.worker_config):
                   # Inject some chaos - random delay or skip
                   chaos_action = random.choice(["delay", "skip", "error"])
                   
                   if chaos_action == "delay":
                       await asyncio.sleep(random.uniform(1, 5))
                   elif chaos_action == "skip":
                       continue
                   # For "error", we'll let the request proceed but it might naturally fail
                       
               success = await self._process_monitored_endpoint(
                   client, endpoint_config, step, len(endpoints)
               )
               
               if not success:
                   return False
                   
           return True
           
       except Exception as e:
           print(f"Sequence execution failed for worker {self.worker_config.worker_id}: {e}")
           return False
           
   async def _process_monitored_endpoint(self, client: HttpClient,
                                       endpoint_config: Dict[str, Any],
                                       step: int, total_steps: int) -> bool:
       """Process endpoint with detailed monitoring"""
       
       endpoint_url = endpoint_config["url"]
       method = endpoint_config.get("method", "GET")
       request_start_time = time.time()
       
       try:
           # Prepare request (reuse existing logic from parent class)
           if "depends_on" in endpoint_config and endpoint_config["depends_on"]:
               format_dict = self._create_format_dict(client)
               result = self._prepare_request_with_dependencies(
                   endpoint_config, format_dict, self.worker_config.worker_id,
                   endpoint_url, endpoint_config.get("id")
               )
               
               if result is None:
                   # Record failed request
                   self._record_request_metric(
                       endpoint_url, method, request_start_time, 0, False,
                       "Dependency preparation failed"
                   )
                   return False
                   
               endpoint_url, endpoint_id, endpoint_data = result
           else:
               endpoint_data = endpoint_config.get("data")
               
           # Prepare file path if needed
           file_path = self._prepare_file_path(endpoint_config)
           form_name = endpoint_config.get("form_name", "file")
           
           # Execute request
           response_data, continue_sequence = await client.make_request(
               endpoint=endpoint_url,
               method=method,
               headers=endpoint_config.get("headers"),
               data=endpoint_data,
               extract=endpoint_config.get("extract"),
               thread_id=self.worker_config.worker_id,
               endpoint_id=endpoint_config.get("id"),
               file_path=file_path,
               form_name=form_name,
               current_step=step,
               total_steps=total_steps,
           )
           
           request_end_time = time.time()
           response_time = request_end_time - request_start_time
           
           # Record successful request
           self._record_request_metric(
               endpoint_url, method, request_start_time, response_time, True, None
           )
           
           return continue_sequence
           
       except Exception as e:
           request_end_time = time.time()
           response_time = request_end_time - request_start_time
           
           # Record failed request
           self._record_request_metric(
               endpoint_url, method, request_start_time, response_time, False, str(e)
           )
           
           return False
           
   def _record_request_metric(self, endpoint: str, method: str, start_time: float,
                            response_time: float, success: bool, error_message: str = None):
       """Record request metric with monitoring"""
       # Simulate status code (in real implementation, get from response)
       status_code = random.choice([200, 201]) if success else random.choice([400, 401, 500, 502, 503])
       
       metric = RequestMetric(
           timestamp=start_time,
           worker_id=self.worker_config.worker_id,
           endpoint=endpoint,
           method=method,
           response_time=response_time,
           status_code=status_code,
           success=success,
           error_message=error_message,
           test_type=self.test_type.name
       )
       
       self.monitor.record_request(metric)


class AsyncioTestRunner:
   """Main runner for enhanced asyncio load tests"""
   
   def __init__(self, config: Dict[str, Any], test_type: str = "standard"):
       self.config = config
       self.test_type_name = test_type
       self.test_type_instance = create_test_type(test_type, config)
       self.monitor = AsyncioMonitor(test_type)
       self.workers: List[EnhancedTestSequence] = []
       self.worker_tasks: List[asyncio.Task] = []
       self.is_running = False
       
   async def run_test(self, user_params: Dict[str, Any] = None) -> bool:
       """Run the complete load test"""
       try:
           # Get test parameters
           test_params = self.test_type_instance.get_test_parameters(user_params)
           
           print(f"\\n Starting {self.test_type_name.upper()} test")
           print(f" Concurrency: {test_params.concurrency} workers")
           print(f" Duration: {test_params.duration}s")
           print(f" Ramp-up: {test_params.ramp_up_time}s")
           print(f" Cool-down: {test_params.cool_down_time}s")
           
           # Start monitoring
           self.monitor.start_test()
           self.is_running = True
           
           # Start live monitoring task
           monitor_task = asyncio.create_task(
               self.monitor.live_monitoring_task(interval=3)
           )
           
           # Generate worker configurations
           worker_configs = self.test_type_instance.generate_worker_configs(test_params)
           
           # Create worker sequences
           self.workers = []
           for worker_config in worker_configs:
               worker_sequence = EnhancedTestSequence(
                   self.config, self.monitor, worker_config, self.test_type_instance
               )
               self.workers.append(worker_sequence)
               
           # Start workers
           self.worker_tasks = []
           for worker in self.workers:
               task = asyncio.create_task(worker.run_sequence_with_monitoring())
               self.worker_tasks.append(task)
               
           # Run test for specified duration or until completion
           test_start_time = time.time()
           
           while self.is_running:
               current_time = time.time()
               elapsed_time = current_time - test_start_time
               
               # Check if test should continue
               stats = self.monitor.get_current_stats()
               
               if not self.test_type_instance.should_continue_test(
                   elapsed_time, 0, stats.get("error_rate", 0)
               ):
                   print(f"\\n  Stopping test due to error threshold")
                   break
                   
               if test_params.duration and elapsed_time >= test_params.duration:
                   print(f"\\n Test duration reached")
                   break
                   
               # Check if all workers are done
               if all(task.done() for task in self.worker_tasks):
                   print(f"\\n All workers completed")
                   break
                   
               await asyncio.sleep(1)
               
           # Stop all workers
           for worker in self.workers:
               worker.stop()
               
           # Wait for workers to finish
           if self.worker_tasks:
               await asyncio.gather(*self.worker_tasks, return_exceptions=True)
               
           # Stop monitoring
           monitor_task.cancel()
           self.monitor.end_test()
           self.is_running = False
           
           # Generate and display report
           await self._generate_reports()
           
           return True
           
       except Exception as e:
           print(f"\\n Test execution failed: {e}")
           traceback.print_exc()
           self.is_running = False
           return False
           
   async def _generate_reports(self):
       """Generate comprehensive test reports"""
       try:
           # Print summary to console
           self.monitor.print_summary(
               context_file=self.config.get("_context_file"),
               base_url=self.config.get("base_url")
           )
           
           # Export detailed reports
           self.monitor.export_csv_report()
           self.monitor.export_json_report()
           
       except Exception as e:
           print(f"  Report generation failed: {e}")
           
   def get_test_types(self) -> List[str]:
       """Get list of available test types"""
       from .test_types import TEST_TYPES
       return list(TEST_TYPES.keys())


# Convenience function for running enhanced asyncio tests
async def run_enhanced_asyncio_test(config: Dict[str, Any], test_type: str = "standard",
                                  user_params: Dict[str, Any] = None) -> bool:
   """
   Run an enhanced asyncio load test
   
   Args:
       config: Test configuration dictionary
       test_type: Type of test (standard, stress, smoke, chaos)
       user_params: User-specified parameters to override defaults
       
   Returns:
       True if test completed successfully
   """
   runner = AsyncioTestRunner(config, test_type)
   return await runner.run_test(user_params)
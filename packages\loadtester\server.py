from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, File, UploadFile, Form
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import jwt
import time
import random
import uvicorn
from pathlib import Path

app = FastAPI(title="Load Test API", version="1.0.0")

SECRET_KEY = "your-secret-key-here-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_HOURS = 24

security = HTTPBearer()

# Data models
class LoginRequest(BaseModel):
    username: str
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int

class CreateItemRequest(BaseModel):
    name: str
    description: str
    price: float
    quantity: int = 1

class ItemResponse(BaseModel):
    id: str
    name: str
    description: str
    price: float
    quantity: int
    created_at: datetime

class UserProfile(BaseModel):
    id: str
    username: str
    email: str
    full_name: str
    created_at: datetime

# In-memory storage
items_db: Dict[str, ItemResponse] = {}
users_db = {
    "testuser": {
        "password": "testpass123",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "id": "user-001"
    }
}

# Helper functions
def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    token = credentials.credentials
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(status_code=401, detail="Invalid authentication credentials")
        return username
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid authentication credentials")

# Health check endpoint
@app.get("/")
async def root():
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "Load Test API",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime": time.time()
    }

# Authentication endpoints
@app.post("/auth/login", response_model=TokenResponse)
async def login(request: LoginRequest):
    # Simulate some processing time
    await asyncio.sleep(random.uniform(0.01, 0.05))
    
    if request.username not in users_db:
        raise HTTPException(status_code=401, detail="Invalid username or password")
    
    if users_db[request.username]["password"] != request.password:
        raise HTTPException(status_code=401, detail="Invalid username or password")
    
    access_token = create_access_token(data={"sub": request.username})
    return TokenResponse(
        access_token=access_token,
        expires_in=ACCESS_TOKEN_EXPIRE_HOURS * 3600
    )

@app.get("/auth/validate")
async def validate_token(username: str = Depends(verify_token)):
    return {"valid": True, "username": username}

# User endpoints
@app.get("/api/user/profile", response_model=UserProfile)
async def get_user_profile(username: str = Depends(verify_token)):
    if username not in users_db:
        raise HTTPException(status_code=404, detail="User not found")
    
    user = users_db[username]
    return UserProfile(
        id=user["id"],
        username=username,
        email=user["email"],
        full_name=user["full_name"],
        created_at=datetime.utcnow()
    )

@app.put("/api/user/profile")
async def update_user_profile(
    full_name: Optional[str] = None,
    email: Optional[str] = None,
    username: str = Depends(verify_token)
):
    if username not in users_db:
        raise HTTPException(status_code=404, detail="User not found")
    
    if full_name:
        users_db[username]["full_name"] = full_name
    if email:
        users_db[username]["email"] = email
    
    return {"message": "Profile updated successfully", "username": username}

# Item CRUD endpoints
@app.post("/api/items", response_model=ItemResponse)
async def create_item(item: CreateItemRequest, username: str = Depends(verify_token)):
    # Simulate processing
    await asyncio.sleep(random.uniform(0.02, 0.1))
    
    item_id = f"item-{len(items_db) + 1:04d}"
    new_item = ItemResponse(
        id=item_id,
        name=item.name,
        description=item.description,
        price=item.price,
        quantity=item.quantity,
        created_at=datetime.utcnow()
    )
    items_db[item_id] = new_item
    return new_item

@app.get("/api/items")
async def list_items(
    limit: int = 10,
    offset: int = 0,
    username: str = Depends(verify_token)
):
    # Simulate database query
    await asyncio.sleep(random.uniform(0.01, 0.05))
    
    all_items = list(items_db.values())
    return {
        "items": all_items[offset:offset + limit],
        "total": len(all_items),
        "limit": limit,
        "offset": offset
    }

@app.get("/api/items/{item_id}", response_model=ItemResponse)
async def get_item(item_id: str, username: str = Depends(verify_token)):
    if item_id not in items_db:
        raise HTTPException(status_code=404, detail="Item not found")
    return items_db[item_id]

@app.put("/api/items/{item_id}")
async def update_item(
    item_id: str,
    item: CreateItemRequest,
    username: str = Depends(verify_token)
):
    if item_id not in items_db:
        raise HTTPException(status_code=404, detail="Item not found")
    
    existing_item = items_db[item_id]
    existing_item.name = item.name
    existing_item.description = item.description
    existing_item.price = item.price
    existing_item.quantity = item.quantity
    
    return {"message": "Item updated successfully", "item_id": item_id}

@app.delete("/api/items/{item_id}")
async def delete_item(item_id: str, username: str = Depends(verify_token)):
    if item_id not in items_db:
        raise HTTPException(status_code=404, detail="Item not found")
    
    del items_db[item_id]
    return {"message": "Item deleted successfully", "item_id": item_id}

# File upload endpoint
@app.post("/api/upload")
async def upload_file(
    file: UploadFile = File(...),
    description: str = Form(None),
    username: str = Depends(verify_token)
):
    # Simulate file processing
    await asyncio.sleep(random.uniform(0.05, 0.2))
    
    contents = await file.read()
    return {
        "filename": file.filename,
        "size": len(contents),
        "content_type": file.content_type,
        "description": description,
        "uploaded_by": username,
        "upload_id": f"upload-{int(time.time())}"
    }

# Search endpoint with complex processing
class SearchRequest(BaseModel):
    query: str
    filters: Optional[Dict[str, Any]] = None

@app.post("/api/search")
async def search_items(
    request: SearchRequest,
    username: str = Depends(verify_token)
):
    # Simulate complex search operation
    await asyncio.sleep(random.uniform(0.1, 0.3))
    
    results = []
    for item_id, item in items_db.items():
        if request.query.lower() in item.name.lower() or request.query.lower() in item.description.lower():
            results.append(item)
    
    return {
        "query": request.query,
        "filters": request.filters,
        "results": results[:10],
        "total_results": len(results),
        "search_time_ms": random.randint(50, 200)
    }

# Batch operations endpoint
@app.post("/api/batch/items")
async def create_batch_items(
    items: List[CreateItemRequest],
    username: str = Depends(verify_token)
):
    # Simulate batch processing
    await asyncio.sleep(random.uniform(0.1, 0.5))
    
    created_items = []
    for item in items:
        item_id = f"item-{len(items_db) + 1:04d}"
        new_item = ItemResponse(
            id=item_id,
            name=item.name,
            description=item.description,
            price=item.price,
            quantity=item.quantity,
            created_at=datetime.utcnow()
        )
        items_db[item_id] = new_item
        created_items.append(new_item)
    
    return {
        "created": len(created_items),
        "items": created_items
    }

# Stress test endpoint (intentionally slow)
@app.get("/api/stress/cpu")
async def stress_cpu(iterations: int = 1000000, username: str = Depends(verify_token)):
    # CPU intensive operation
    start = time.time()
    result = 0
    for i in range(iterations):
        result += i ** 2
    
    return {
        "iterations": iterations,
        "result": result,
        "processing_time": time.time() - start
    }

@app.get("/api/stress/memory")
async def stress_memory(size_mb: int = 10, username: str = Depends(verify_token)):
    # Memory intensive operation
    if size_mb > 100:
        raise HTTPException(status_code=400, detail="Size too large (max 100MB)")
    
    data = bytearray(size_mb * 1024 * 1024)
    return {
        "allocated_mb": size_mb,
        "status": "completed"
    }

# Analytics endpoint
@app.get("/api/analytics/summary")
async def get_analytics_summary(username: str = Depends(verify_token)):
    return {
        "total_items": len(items_db),
        "total_users": len(users_db),
        "api_calls_today": random.randint(1000, 10000),
        "average_response_time_ms": random.uniform(50, 200),
        "error_rate": random.uniform(0.001, 0.05),
        "timestamp": datetime.utcnow().isoformat()
    }

import asyncio

if __name__ == "__main__":
    print("Starting FastAPI test server on http://localhost:8001")
    print("Username: testuser")
    print("Password: testpass123")
    print("API Docs: http://localhost:8001/docs")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
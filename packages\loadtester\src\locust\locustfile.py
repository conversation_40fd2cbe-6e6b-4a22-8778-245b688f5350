import os
import sys
import logging
import re
import json
import random
from pathlib import Path
from typing import Dict, Any, Optional, List

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from locust import HttpUser, task, between, events

# Import Test.Charge Locust integration
try:
    # Try relative import first (when run as module)
    from .charge import (
        TestChargeUser,
        ConfiguredTestUser,
        ScenarioBasedUser,
        setup_locust_environment,
    )
except ImportError:
    # Fallback to absolute import (when run by Locust directly)
    import sys
    from pathlib import Path

    # Add the src directory to path
    src_dir = Path(__file__).parent.parent
    if str(src_dir) not in sys.path:
        sys.path.insert(0, str(src_dir))

    from locust.charge import (
        TestChargeUser,
        ConfiguredTestUser,
        ScenarioBasedUser,
        setup_locust_environment,
    )

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration from environment variables
EXECUTION_MODE = os.environ.get("EXECUTION_MODE", "batch")  # single|batch|gradual
USER_TYPES = os.environ.get("USER_TYPES", "all")  # all|conversation|upload|health|burst
MAX_CONVERSATIONS = int(os.environ.get("MAX_CONVERSATIONS", "3"))
BATCH_SIZE = int(os.environ.get("BATCH_SIZE", "5"))
WAIT_MIN = float(os.environ.get("WAIT_MIN", "1"))
WAIT_MAX = float(os.environ.get("WAIT_MAX", "3"))

logger.info(f"Locust Configuration:")
logger.info(f"  - Execution Mode: {EXECUTION_MODE}")
logger.info(f"  - User Types: {USER_TYPES}")
logger.info(f"  - Max Conversations: {MAX_CONVERSATIONS}")
logger.info(f"  - Batch Size: {BATCH_SIZE}")
logger.info(f"  - Wait Time: {WAIT_MIN}-{WAIT_MAX}s")


class BaseTestUser(HttpUser):
    """Base test user with enhanced dependency management and flexible execution modes"""

    abstract = True
    wait_time = between(WAIT_MIN, WAIT_MAX)
    host = None

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.extracted_variables = {}
        self.config_data = {}
        self.conversation_count = 0
        self.execution_mode = EXECUTION_MODE

        # Set host from config if not already set
        if not self.host and hasattr(self, "config_data") and self.config_data:
            self.host = self.config_data.get("base_url")
            logger.info(f"Host set to: {self.host}")

    def on_start(self):
        """Initialize user with proper configuration loading"""
        logger.info(f"{self.__class__.__name__} started (mode: {self.execution_mode})")

        context = os.environ.get("CONFIG_CONTEXT", "andoc")
        logger.info(f"Using context: {context}")

        self._load_configuration(context)
        self._setup_authentication()

        # Set host from config if not set via CLI
        if not self.host and self.config_data.get("base_url"):
            self.host = self.config_data["base_url"]
            logger.info(f"Host set from config: {self.host}")

        self.client.verify = self.config_data.get("verify_ssl", True)
        logger.info(f"SSL verification set to: {self.client.verify}")

    def _load_configuration(self, context: str):
        """Load configuration from files"""
        try:
            config_path = project_root / "configs" / f"{context}.json"
            global_config_path = project_root / "config.global.json"

            config_data = {}

            if global_config_path.exists():
                with open(global_config_path, "r", encoding="utf-8") as f:
                    config_data.update(json.load(f))
                logger.info("Loaded global config")

            if config_path.exists():
                with open(config_path, "r", encoding="utf-8") as f:
                    config_data.update(json.load(f))
                logger.info(f"Loaded context config: {context}")

            self.config_data = config_data

        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            self.config_data = {}

    def _setup_authentication(self):
        """Set up JWT authentication headers"""
        if self.config_data.get("jwt_token"):
            self.client.headers.update(
                {"X-CGPT-AUTHORIZATION": f"Bearer {self.config_data['jwt_token']}"}
            )
            logger.info("JWT authentication configured")

    def _extract_values_from_response(
        self, response_data: Dict[str, Any], extract_config: List[Dict[str, str]]
    ):
        """Extract values from response according to configuration"""
        if not extract_config:
            return

        for extraction in extract_config:
            if "key" in extraction and "value_from" in extraction:
                key_id = extraction["key"]
                value_key = extraction["value_from"]

                if isinstance(response_data, dict) and value_key in response_data:
                    extracted_value = response_data[value_key]
                    self.extracted_variables[key_id] = extracted_value
                    logger.info(f"Extracted {key_id} = {extracted_value}")

    def _format_string_with_variables(self, template: str) -> str:
        """Format string template with extracted variables"""
        if not isinstance(template, str) or "{" not in template:
            return template

        var_pattern = r"\{([^{}]+)\}"
        required_vars = re.findall(var_pattern, template)

        missing_vars = [
            var for var in required_vars if var not in self.extracted_variables
        ]
        if missing_vars:
            logger.error(f"Missing variables for formatting: {missing_vars}")
            logger.error(
                f"Available variables: {list(self.extracted_variables.keys())}"
            )
            return template

        try:
            return template.format(**self.extracted_variables)
        except KeyError as e:
            logger.error(f"Error formatting template: {e}")
            return template

    def _format_data_with_variables(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Format data dictionary with extracted variables"""
        if not data:
            return data

        formatted_data = {}
        for key, value in data.items():
            if isinstance(value, str):
                formatted_data[key] = self._format_string_with_variables(value)
            else:
                formatted_data[key] = value
        return formatted_data

    def _execute_endpoint(
        self, endpoint_config: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Execute a single endpoint with proper dependency handling"""
        url = endpoint_config.get("url", "/")
        method = endpoint_config.get("method", "GET").upper()
        endpoint_id = endpoint_config.get("id", f"{method} {url}")
        data = endpoint_config.get("data", {})
        params = endpoint_config.get("params", {})
        file_path = endpoint_config.get("file")
        form_name = endpoint_config.get("form_name", "file")

        # Format data and params with extracted variables
        formatted_data = self._format_data_with_variables(data)
        formatted_params = self._format_data_with_variables(params)

        # Format URL parameters into the URL
        if formatted_params:
            url_params = "&".join([f"{k}={v}" for k, v in formatted_params.items()])
            separator = "&" if "?" in url else "?"
            url = f"{url}{separator}{url_params}"

        try:
            response = None
            if method == "GET":
                response = self.client.get(url, name=endpoint_id)
            elif method == "POST":
                if file_path:
                    response = self._upload_file(url, file_path, form_name, endpoint_id)
                else:
                    response = self.client.post(
                        url, json=formatted_data, name=endpoint_id
                    )
            else:
                logger.warning(f"Unsupported method: {method}")
                return None

            if response.status_code < 400:
                try:
                    response_data = response.json()
                    extract_config = endpoint_config.get("extract", [])
                    if extract_config:
                        self._extract_values_from_response(
                            response_data, extract_config
                        )

                    logger.debug(f"Success: {method} {url} -> {response.status_code}")
                    return response_data

                except Exception as json_error:
                    logger.warning(
                        f"Could not parse JSON response from {url}: {json_error}"
                    )
                    return {"text": response.text}
            else:
                logger.warning(f"Failed: {method} {url} -> {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Error executing {method} {url}: {e}")
            return None

    def _upload_file(self, url: str, file_path: str, form_name: str, endpoint_id: str):
        """Handle file upload requests"""
        import os
        import mimetypes

        full_file_path = (
            os.path.join("upload", file_path)
            if not os.path.isabs(file_path)
            else file_path
        )

        if not os.path.exists(full_file_path):
            logger.error(f"File not found: {full_file_path}")
            return None

        try:
            file_type, _ = mimetypes.guess_type(full_file_path)
            if not file_type:
                file_type = "application/octet-stream"

            with open(full_file_path, "rb") as f:
                file_content = f.read()

            files = {form_name: (os.path.basename(file_path), file_content, file_type)}
            response = self.client.post(url, files=files, name=endpoint_id)
            logger.info(f"Uploaded file {file_path} to {url}")
            return response

        except Exception as e:
            logger.error(f"Error uploading file {file_path}: {e}")
            return None

    def _get_conversation_endpoints(self, session_number: int) -> List[Dict[str, Any]]:
        """Get endpoints for a specific conversation session"""
        if not self.config_data or "endpoints" not in self.config_data:
            return []

        all_endpoints = self.config_data["endpoints"]
        conversation_endpoints = []

        # Find endpoints for this conversation session
        session_pattern = f"session-{session_number}"

        for endpoint in all_endpoints:
            endpoint_id = endpoint.get("id", "")
            if session_pattern in endpoint_id or f"-{session_number}" in endpoint_id:
                conversation_endpoints.append(endpoint)

        # If no specific session found, use first conversation pattern
        if not conversation_endpoints and session_number == 1:
            for endpoint in all_endpoints:
                endpoint_id = endpoint.get("id", "")
                if "conversation" in endpoint_id and any(
                    x in endpoint_id for x in ["session-1", "-1"]
                ):
                    conversation_endpoints.append(endpoint)

        return conversation_endpoints

    def _get_upload_endpoints(self) -> List[Dict[str, Any]]:
        """Get file upload related endpoints"""
        if not self.config_data or "endpoints" not in self.config_data:
            return []

        upload_endpoints = []
        all_endpoints = self.config_data["endpoints"]

        # Find upload sessions (typically session 3 and 4 have uploads)
        for endpoint in all_endpoints:
            endpoint_id = endpoint.get("id", "")
            if (
                "analyse" in endpoint_id
                or "upload" in endpoint_id
                or "document" in endpoint_id
                or endpoint.get("file")
            ):
                upload_endpoints.append(endpoint)

        return upload_endpoints


class ConversationUser(BaseTestUser):
    """User focused on conversation-only endpoints"""

    weight = 3 if USER_TYPES in ["all", "conversation"] else 0

    @task
    def run_conversation(self):
        """Run conversation based on execution mode"""
        if self.execution_mode == "single":
            self._run_single_conversation()
        elif self.execution_mode == "batch":
            self._run_batch_conversations()
        elif self.execution_mode == "gradual":
            self._run_gradual_conversations()

    def _run_single_conversation(self):
        """Run a single conversation session (5-6 messages)"""
        # Create session
        session_result = self._execute_endpoint(
            {
                "url": "/andoc/session/new",
                "method": "POST",
                "extract": [
                    {"key": "conversation-session-single", "value_from": "session_id"}
                ],
                "id": "creation-conversation-session-single",
            }
        )

        if not session_result:
            return

        # Send a few messages
        messages = [
            "Salut",
            "Tu peux faire quoi pour moi ?",
            "Es-tu meilleur que Copilot de Microsoft ?",
            "Merci!",
        ]

        for i, message in enumerate(messages, 1):
            self.wait()
            result = self._execute_endpoint(
                {
                    "url": "/andoc/chat/send",
                    "method": "POST",
                    "data": {
                        "message": message,
                        "session_id": "{conversation-session-single}",
                    },
                    "id": f"message-single-{i}",
                }
            )
            if not result:
                break

    def _run_batch_conversations(self):
        """Run multiple complete conversation sessions"""
        for session_num in range(1, MAX_CONVERSATIONS + 1):
            logger.info(f"Starting conversation session {session_num}")

            # Reset variables for new session
            session_key = f"conversation-session-{session_num}"

            # Create session
            session_result = self._execute_endpoint(
                {
                    "url": "/andoc/session/new",
                    "method": "POST",
                    "extract": [{"key": session_key, "value_from": "session_id"}],
                    "id": f"creation-conversation-session-{session_num}",
                }
            )

            if not session_result:
                continue

            # Get conversation endpoints for this session
            conversation_endpoints = self._get_conversation_endpoints(session_num)
            if not conversation_endpoints:
                # Use default messages
                messages = [
                    "Salut",
                    "Allo. Tu peux faire quoi pour moi ?",
                    "Es-tu meilleur que Copilot de Microsoft ?",
                    "Qui t'a crÃ©Ã© ? et Quand? Selon quelles procÃ©dÃ©s?",
                    "Est-ce qu'il va y avoir des nouveautÃ©s pour mIA en 2025?",
                ]

                for i, message in enumerate(messages, 1):
                    self.wait()
                    result = self._execute_endpoint(
                        {
                            "url": "/andoc/chat/send",
                            "method": "POST",
                            "data": {
                                "message": message,
                                "session_id": f"{{{session_key}}}",
                            },
                            "id": f"message-{session_num}-{i}",
                        }
                    )
                    if not result:
                        break
            else:
                # Use configured endpoints
                for endpoint in conversation_endpoints:
                    self.wait()
                    result = self._execute_endpoint(endpoint)
                    if not result:
                        break

            logger.info(f"Completed conversation session {session_num}")

    def _run_gradual_conversations(self):
        """Run conversations in smaller batches"""
        if not self.config_data or "endpoints" not in self.config_data:
            return

        all_endpoints = self.config_data["endpoints"]

        # Filter to conversation-only endpoints
        conv_endpoints = [
            ep
            for ep in all_endpoints
            if "conversation" in ep.get("id", "") or "chat" in ep.get("url", "")
        ]

        # Process in batches
        for i in range(0, len(conv_endpoints), BATCH_SIZE):
            batch = conv_endpoints[i : i + BATCH_SIZE]
            logger.info(f"Processing conversation batch {i//BATCH_SIZE + 1}")

            for endpoint in batch:
                result = self._execute_endpoint(endpoint)
                if not result:
                    logger.warning(f"Failed endpoint in batch, continuing...")
                self.wait()

            # Longer wait between batches
            self.wait()


class FileUploadUser(BaseTestUser):
    """User focused on file upload and analysis endpoints"""

    weight = 2 if USER_TYPES in ["all", "upload"] else 0
    wait_time = between(WAIT_MIN * 3, WAIT_MAX * 3)  # Longer waits for file processing

    @task
    def run_file_analysis(self):
        """Run file upload and analysis session"""
        # Create analysis session
        session_result = self._execute_endpoint(
            {
                "url": "/andoc/session/new",
                "method": "POST",
                "extract": [{"key": "analysis-session", "value_from": "session_id"}],
                "id": "creation-analysis-session",
            }
        )

        if not session_result:
            return

        # Choose a file to upload
        files = ["Reponses_sondage.xlsx"]  # Start with smaller file
        file_to_upload = random.choice(files)

        # Upload file
        upload_result = self._execute_endpoint(
            {
                "url": "/andoc/document/upload",
                "method": "POST",
                "file": file_to_upload,
                "form_name": "file",
                "id": "upload-analysis-file",
                "params": {"session_id": "{analysis-session}"},
            }
        )

        if upload_result:
            # Ask analysis questions
            questions = [
                "RÃ©sume ce document briÃ¨vement",
                "Quelles sont les principales tendances ?",
            ]

            for i, question in enumerate(questions, 1):
                self.wait()  # Wait for file processing
                self._execute_endpoint(
                    {
                        "url": "/andoc/chat/send",
                        "method": "POST",
                        "data": {
                            "message": question,
                            "is_restricted": True,
                            "session_id": "{analysis-session}",
                        },
                        "id": f"analysis-question-{i}",
                    }
                )


class HealthCheckUser(BaseTestUser):
    """Lightweight user for health checks and session creation only"""

    weight = 1 if USER_TYPES in ["all", "health"] else 0
    wait_time = between(WAIT_MIN * 2, WAIT_MAX * 2)

    @task
    def health_check(self):
        """Perform basic health check"""
        self._execute_endpoint(
            {
                "url": "/andoc/session/new",
                "method": "POST",
                "id": "health-check-session",
            }
        )


class BurstTestUser(BaseTestUser):
    """User that creates burst traffic patterns"""

    weight = 1 if USER_TYPES in ["all", "burst"] else 0
    wait_time = between(0.5, 1.5)

    @task
    def burst_requests(self):
        """Create burst of session creation requests"""
        burst_size = random.randint(2, 4)

        for i in range(burst_size):
            self._execute_endpoint(
                {
                    "url": "/andoc/session/new",
                    "method": "POST",
                    "extract": [
                        {"key": f"burst-session-{i}", "value_from": "session_id"}
                    ],
                    "id": f"burst-session-{i}",
                }
            )

            if i < burst_size - 1:  # Don't wait after last request
                self.wait()


# Global event handlers
@events.init.add_listener
def on_locust_init(environment, **kwargs):
    """Initialize Test.Charge integration when Locust starts"""
    logger.info("Test.Charge Optimized Integration Starting...")
    logger.info(f"Configuration:")
    logger.info(f"  - Context: {os.environ.get('CONFIG_CONTEXT', 'andoc')}")
    logger.info(f"  - Execution Mode: {EXECUTION_MODE}")
    logger.info(f"  - User Types: {USER_TYPES}")
    logger.info(f"  - Max Conversations: {MAX_CONVERSATIONS}")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Handle test start event"""
    logger.info("Load test starting...")

    # Show active user classes
    active_classes = []
    if ConversationUser.weight > 0:
        active_classes.append(f"ConversationUser ({ConversationUser.weight})")
    if FileUploadUser.weight > 0:
        active_classes.append(f"FileUploadUser ({FileUploadUser.weight})")
    if HealthCheckUser.weight > 0:
        active_classes.append(f"HealthCheckUser ({HealthCheckUser.weight})")
    if BurstTestUser.weight > 0:
        active_classes.append(f"BurstTestUser ({BurstTestUser.weight})")

    logger.info(f"Active User Classes: {', '.join(active_classes)}")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Handle test stop event"""
    logger.info("Load test completed!")

    if hasattr(environment, "stats") and environment.stats.entries:
        total_requests = sum(
            stats.num_requests for stats in environment.stats.entries.values()
        )
        total_failures = sum(
            stats.num_failures for stats in environment.stats.entries.values()
        )

        if total_requests > 0:
            success_rate = ((total_requests - total_failures) / total_requests) * 100

            logger.info(f"Test Summary:")
            logger.info(f"   â€¢ Total Requests: {total_requests:,}")
            logger.info(f"   â€¢ Failed Requests: {total_failures:,}")
            logger.info(f"   â€¢ Success Rate: {success_rate:.1f}%")


# Setup Test.Charge integration when locustfile is loaded
@events.init.add_listener
def on_locust_init(environment, **kwargs):
    """Initialize Test.Charge integration when Locust starts"""
    try:
        setup_locust_environment(environment)
        logger.info("Test.Charge integration initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Test.Charge integration: {e}")
        # Continue with basic Locust functionality


if __name__ == "__main__":
    print("Test.Charge Optimized Locust Integration")
    print("=" * 50)
    print(f"Execution Mode: {EXECUTION_MODE}")
    print(f"User Types: {USER_TYPES}")
    print(f"Configuration Context: {os.environ.get('CONFIG_CONTEXT', 'andoc')}")

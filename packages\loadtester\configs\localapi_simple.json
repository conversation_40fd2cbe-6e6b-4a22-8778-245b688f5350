{"base_url": "http://localhost:8001", "verify_ssl": false, "num_threads": 5, "jwt_token": "", "endpoints": [{"id": "health", "url": "/health", "method": "GET", "description": "Health check (no auth)"}, {"id": "login", "url": "/auth/login", "method": "POST", "data": {"username": "testuser", "password": "testpass123"}, "extract": [{"key": "token", "valueFrom": "access_token"}]}, {"id": "validate", "url": "/auth/validate", "method": "GET", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}}, {"id": "profile", "url": "/api/user/profile", "method": "GET", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}}, {"id": "create_item", "url": "/api/items", "method": "POST", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "data": {"name": "Test Item", "description": "A test item for load testing", "price": 99.99, "quantity": 10}, "extract": [{"key": "item_id", "valueFrom": "id"}]}, {"id": "get_item", "url": "/api/items/{item_id}", "method": "GET", "depends_on": "create_item", "headers": {"Authorization": "Bearer {token}"}}, {"id": "list_items", "url": "/api/items", "method": "GET", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}}, {"id": "search", "url": "/api/search", "method": "POST", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "data": {"query": "test"}}, {"id": "analytics", "url": "/api/analytics/summary", "method": "GET", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}}]}
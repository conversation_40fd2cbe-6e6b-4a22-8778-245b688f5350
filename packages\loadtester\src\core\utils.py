import httpx
import json
import base64
import datetime
from typing import Dict, Any, Optional, Tu<PERSON>

def decode_jwt_payload(jwt_token: str) -> Dict[str, Any]:
    """Décode la partie payload d'un jeton JWT sans vérifier la signature."""
    try:
        # Enlever le préfixe "Bearer " si présent
        if jwt_token.startswith("Bearer "):
            jwt_token = jwt_token[7:]

        # Un JWT est composé de 3 parties séparées par des points: header.payload.signature
        parts = jwt_token.split(".")
        if len(parts) != 3:
            raise ValueError("Format JWT invalide")

        # Décode la partie payload (2Ã¨me partie)
        payload_base64 = parts[1]
        # Ajuster la longueur pour qu'elle soit un multiple de 4
        payload_base64 += "=" * (-len(payload_base64) % 4)
        # Remplacer les caractÃ¨res URL-safe par des caractÃ¨res base64 standard
        payload_base64 = payload_base64.replace("-", "+").replace("_", "/")

        # Décoder la chaÃ®ne base64
        payload_json = base64.b64decode(payload_base64)
        payload = json.loads(payload_json)

        return payload
    except Exception as e:
        raise ValueError(f"Erreur lors du décodage du JWT: {str(e)}")


async def validate_jwt(
    base_url: str, jwt_token: str, verify_ssl: bool = True
) -> Tuple[bool, str]:
    """
    Valide la validité d'un jeton JWT.
    Retourne un tuple (est_valide, message).
    """
    try:
        # 1. Vérifier la structure et la date d'expiration
        payload = decode_jwt_payload(jwt_token)

        # Vérifier si le jeton a une date d'expiration
        if "exp" in payload:
            exp_timestamp = payload["exp"]
            exp_datetime = datetime.datetime.fromtimestamp(exp_timestamp)
            now = datetime.datetime.now()

            if now > exp_datetime:
                return (
                    False,
                    f"Le jeton JWT a expiré le {exp_datetime.strftime('%Y-%m-%d %H:%M:%S')}",
                )

            # Avertir si le jeton expire bientÃ´t (dans moins d'une heure)
            time_left = exp_datetime - now
            if time_left.total_seconds() < 3600:  # Moins d'une heure
                minutes_left = int(time_left.total_seconds() / 60)
                return (
                    True,
                    f"Attention: Le jeton JWT expire dans {minutes_left} minutes (Ã  {exp_datetime.strftime('%H:%M:%S')})",
                )

        # 2. Faire un appel légerà  l'API pour vérifier que le serveur accepte toujours le jeton
        # Cette étape est optionnelle car certaines APIs n'ont pas d'endpoint "léger" pour tester l'authentification
        try:
            async with httpx.AsyncClient(verify=verify_ssl) as client:
                # Utiliser un endpoint qui ne nécessite pas beaucoup de traitement
                # On suppose que l'endpoint /health ou /ping existe
                test_url = f"{base_url}/health"
                headers = {"X-CGPT-AUTHORIZATION": f"Bearer {jwt_token}"}

                response = await client.get(test_url, headers=headers, timeout=5.0)

                if response.status_code in (401, 403):
                    return (
                        False,
                        f"Le serveur a rejeté le jeton JWT (HTTP {response.status_code})",
                    )
        except Exception as e:
            # Si l'appel de test échoue, on se fie uniquementà  la vérification de la date d'expiration
            print(
                f"Avertissement: Impossible de vérifier le jeton sur le serveur: {str(e)}"
            )

        return True, "Le jeton JWT est valide"
    except Exception as e:
        return False, f"Erreur lors de la validation du JWT: {str(e)}"


def get_config_paths(config_path: str) -> Tuple[str, Optional[str]]:
    """
    Détermine le chemin du fichier global et du contexteà  partir du paramÃ¨tre -c.
    Si le paramÃ¨tre contient un ",", on considÃ¨re le premier comme global et le second comme contexte.
    Sinon, on utilise config.global.json + le paramÃ¨tre comme contexte.
    """
    if "," in config_path:
        parts = [p.strip() for p in config_path.split(",")]
        if len(parts) == 2:
            return parts[0], parts[1]
    # Par défaut, global = config.global.json, contexte = config_path
    return "config.global.json", config_path

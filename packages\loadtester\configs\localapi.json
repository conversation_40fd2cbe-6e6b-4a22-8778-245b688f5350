{"base_url": "http://localhost:8001", "verify_ssl": false, "num_threads": 10, "num_users": 20, "duration": 60, "jwt_token": "", "endpoints": [{"id": "health_check", "url": "/health", "method": "GET", "description": "Health check endpoint (no auth required)", "weight": 5}, {"id": "login", "url": "/auth/login", "method": "POST", "description": "Login to get JWT token", "data": {"username": "testuser", "password": "testpass123"}, "extract": [{"key": "token", "valueFrom": "access_token"}], "weight": 1}, {"id": "validate_token", "url": "/auth/validate", "method": "GET", "description": "Validate JWT token", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "weight": 2}, {"id": "get_profile", "url": "/api/user/profile", "method": "GET", "description": "Get user profile", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "weight": 8}, {"id": "update_profile", "url": "/api/user/profile", "method": "PUT", "description": "Update user profile", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "data": {"full_name": "Updated Test User", "email": "<EMAIL>"}, "weight": 3}, {"id": "create_item", "url": "/api/items", "method": "POST", "description": "Create a new item", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "data": {"name": "Test Item {{random_number}}", "description": "This is a test item created at {{timestamp}}", "price": 99.99, "quantity": 10}, "extract": [{"key": "item_id", "valueFrom": "id"}], "weight": 10}, {"id": "list_items", "url": "/api/items?limit=20&offset=0", "method": "GET", "description": "List all items", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "weight": 15}, {"id": "get_item", "url": "/api/items/{item_id}", "method": "GET", "description": "Get specific item", "depends_on": "create_item", "headers": {"Authorization": "Bearer {token}"}, "weight": 12}, {"id": "update_item", "url": "/api/items/{item_id}", "method": "PUT", "description": "Update an item", "depends_on": "create_item", "headers": {"Authorization": "Bearer {token}"}, "data": {"name": "Updated Item {{random_number}}", "description": "Updated description at {{timestamp}}", "price": 149.99, "quantity": 5}, "weight": 5}, {"id": "search_items", "url": "/api/search", "method": "POST", "description": "Search for items", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "data": {"query": "test", "filters": {"min_price": 0, "max_price": 1000}}, "weight": 8}, {"id": "batch_create", "url": "/api/batch/items", "method": "POST", "description": "Create multiple items", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "data": {"items": [{"name": "Batch Item 1", "description": "First batch item", "price": 29.99, "quantity": 5}, {"name": "Batch Item 2", "description": "Second batch item", "price": 39.99, "quantity": 8}, {"name": "Batch Item 3", "description": "Third batch item", "price": 49.99, "quantity": 3}]}, "weight": 3}, {"id": "analytics_summary", "url": "/api/analytics/summary", "method": "GET", "description": "Get analytics summary", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "weight": 10}, {"id": "stress_cpu", "url": "/api/stress/cpu?iterations=100000", "method": "GET", "description": "CPU stress test endpoint", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "weight": 2}, {"id": "stress_memory", "url": "/api/stress/memory?size_mb=5", "method": "GET", "description": "Memory stress test endpoint", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "weight": 2}, {"id": "upload_file", "url": "/api/upload", "method": "POST", "description": "Upload a file", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "files": {"file": "test_file.txt"}, "data": {"description": "Test file upload at {{timestamp}}"}, "weight": 5}, {"id": "delete_item", "url": "/api/items/{item_id}", "method": "DELETE", "description": "Delete an item", "depends_on": "create_item", "headers": {"Authorization": "Bearer {token}"}, "weight": 3}], "test_scenarios": {"standard": {"description": "Standard load test with mixed operations", "duration": 300, "users": 20, "ramp_up": 30, "think_time_min": 0.5, "think_time_max": 2.0}, "stress": {"description": "High load stress test", "duration": 600, "users": 50, "ramp_up": 60, "think_time_min": 0.1, "think_time_max": 0.5}, "smoke": {"description": "Quick validation test", "duration": 60, "users": 5, "ramp_up": 10, "think_time_min": 1.0, "think_time_max": 3.0}, "chaos": {"description": "Unpredictable load patterns", "duration": 300, "users": 30, "ramp_up": 5, "think_time_min": 0.0, "think_time_max": 5.0, "chaos_injection": true}}, "reporting": {"output_format": ["console", "csv", "json"], "metrics": ["response_time", "throughput", "error_rate", "percentiles"], "percentiles": [50, 75, 90, 95, 99]}}
Fichier de contexte utilisé : configs/localapi_simple.json

===== RéSUMé DU TEST =====
Temps d'exécution total: 2.9002s
Nombre total de requètes: 45
Nombre de threads: 5
Nombre de tÃ¢ches par thread: 9
Nombre total de tÃ¢ches: 45

--- STATISTIQUES PAR ENDPOINT ---

health:
  Nombre: 5
  Min: 0.2745s
  Max: 0.3234s
  Moyenne: 0.2872s
  Médiane: 0.2788s

login:
  Nombre: 5
  Min: 0.3046s
  Max: 0.3084s
  Moyenne: 0.3064s
  Médiane: 0.3058s

validate:
  Nombre: 5
  Min: 0.2670s
  Max: 0.2719s
  Moyenne: 0.2694s
  Médiane: 0.2689s

profile:
  Nombre: 5
  Min: 0.2598s
  Max: 0.2632s
  Moyenne: 0.2608s
  Médiane: 0.2599s

create_item:
  Nombre: 5
  Min: 0.3506s
  Max: 0.3682s
  Moyenne: 0.3611s
  Médiane: 0.3680s

get_item:
  Nombre: 5
  Min: 0.2654s
  Max: 0.2668s
  Moyenne: 0.2661s
  Médiane: 0.2659s

list_items:
  Nombre: 5
  Min: 0.2982s
  Max: 0.3146s
  Moyenne: 0.3105s
  Médiane: 0.3135s

search:
  Nombre: 5
  Min: 0.4205s
  Max: 0.5253s
  Moyenne: 0.4740s
  Médiane: 0.4797s

analytics:
  Nombre: 5
  Min: 0.2635s
  Max: 0.2661s
  Moyenne: 0.2647s
  Médiane: 0.2646s

--- STATISTIQUES PAR THREAD ---
Thread 4: 9 requètes en 2.8064s
Thread 0: 9 requètes en 2.8213s
Thread 2: 9 requètes en 2.7132s
Thread 3: 9 requètes en 2.8522s
Thread 1: 9 requètes en 2.8080s

--- STATISTIQUES DES CODES HTTP ---

SuccÃ¨s (2xx):
  200: 45 occurrences

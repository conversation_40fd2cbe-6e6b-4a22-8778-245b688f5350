# Test.Charge Enhanced Launcher with Locust Support
param(
  [Parameter(Mandatory=$true)]
  [string]$context,
  
  [Parameter(Mandatory=$false)]
  [ValidateSet("asyncio", "locust")]
  [string]$mode = "asyncio",
  
  [Parameter(Mandatory=$false)]
  [ValidateSet("standard", "stress", "smoke", "chaos")]
  [string]$testType = "standard",
  
  [Parameter(Mandatory=$false)]
  [string]$host = "",
  
  [Parameter(Mandatory=$false)]
  [int]$users = 0,
  
  [Parameter(Mandatory=$false)]
  [float]$spawnRate = 0,
  
  [Parameter(Mandatory=$false)]
  [string]$runTime = "",
  
  [Parameter(Mandatory=$false)]
  [switch]$headless,
  
  [Parameter(Mandatory=$false)]
  [switch]$locustUI,
  
  [Parameter(Mandatory=$false)]
  [switch]$skipJWT,
  
  [Parameter(Mandatory=$false)]
  [string]$prometheusUrl = "",
  
  [Parameter(Mandatory=$false)]
  [string]$slackWebhook = "",
  
  [Parameter(Mandatory=$false)]
  [string]$csvOutput = "",
  
  [Parameter(Mandatory=$false)]
  [string]$htmlOutput = ""
)

# Vérifie et active l'environnement virtuel .venv si nécessaire
$venvPath = ".venv"
$venvActivate = Join-Path $venvPath "Scripts\Activate.ps1"

if (Test-Path $venvActivate) {
    # Vérifie si l'environnement est déjà  activé
    if (-not $env:VIRTUAL_ENV) {
        Write-Host "ðŸ”§ Activation de l'environnement virtuel .venv..." -ForegroundColor Blue
        & $venvActivate
    }
} else {
    Write-Host "âš ï¸ Environnement virtuel .venv non trouvé" -ForegroundColor Yellow
}

$contextConfig = "configs/$context.json"

# Vérifie si le fichier de contexte existe
if (-not (Test-Path $contextConfig)) {
    Write-Host "âŒ Fichier de contexte non trouvé: $contextConfig" -ForegroundColor Red
    Write-Host "Contextes disponibles:" -ForegroundColor Yellow
    if (Test-Path "configs") {
        Get-ChildItem "configs" -Filter "*.json" | ForEach-Object { 
            Write-Host "  - $($_.BaseName)" -ForegroundColor Cyan
        }
    }
    exit 1
}

# Construction de la commande
$cmd = @("python", "src/main.py", "-c", $contextConfig, "--mode", $mode)

if ($skipJWT) {
    $cmd += "--skip-jwt-validation"
}

# Arguments spécifiques au mode Locust
if ($mode -eq "locust") {
    
    Write-Host "ðŸš€ Mode Locust sélectionné" -ForegroundColor Magenta
    
    $cmd += @("--test-type", $testType)
    
    if ($host) {
        $cmd += @("--host", $host)
    }
    
    if ($users -gt 0) {
        $cmd += @("--users", $users.ToString())
    }
    
    if ($spawnRate -gt 0) {
        $cmd += @("--spawn-rate", $spawnRate.ToString())
    }
    
    if ($runTime) {
        $cmd += @("--run-time", $runTime)
    }
    
    if ($headless -and -not $locustUI) {
        $cmd += "--headless"
    }
    
    if ($locustUI) {
        $cmd += "--locust-ui"
    }
    
    if ($prometheusUrl) {
        $cmd += @("--prometheus-url", $prometheusUrl)
    }
    
    if ($slackWebhook) {
        $cmd += @("--slack-webhook", $slackWebhook)
    }
    
    if ($csvOutput) {
        $cmd += @("--csv-output", $csvOutput)
    }
    
    if ($htmlOutput) {
        $cmd += @("--html-output", $htmlOutput)
    }
}

# Affichage des informations
Write-Host ""
Write-Host "ðŸŽ¯ Configuration du test" -ForegroundColor Yellow
Write-Host "=========================" -ForegroundColor Yellow
Write-Host "ðŸ“‹ Contexte: $context" -ForegroundColor Cyan
Write-Host "ðŸ”§ Mode: $mode" -ForegroundColor Cyan

if ($mode -eq "locust") {
    Write-Host "ðŸ§ª Type de test: $testType" -ForegroundColor Cyan
    if ($host) { Write-Host "ðŸŒ HÃ´te: $host" -ForegroundColor Cyan }
    if ($users -gt 0) { Write-Host "ðŸ‘¥ Utilisateurs: $users" -ForegroundColor Cyan }
    if ($spawnRate -gt 0) { Write-Host "âš¡ Taux de spawn: $spawnRate/sec" -ForegroundColor Cyan }
    if ($runTime) { Write-Host "â±ï¸ Durée: $runTime" -ForegroundColor Cyan }
    
    $modeText = if ($headless -and -not $locustUI) { "Headless" } else { "Interface Web" }
    Write-Host "ðŸ–¥ï¸ Mode UI: $modeText" -ForegroundColor Cyan
    
    if (-not $headless -or $locustUI) {
        Write-Host "ðŸŒ Interface web disponible sur: http://localhost:8089" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "ðŸ’» Commande: $($cmd -join ' ')" -ForegroundColor Gray
Write-Host ""

# Exemples d'utilisation si c'est juste un appel d'aide
if ($context -eq "help" -or $context -eq "-help" -or $context -eq "--help") {
    Write-Host @"
ðŸš€ Test.Charge - Launcher Enhanced avec support Locust
====================================================

USAGE BASIQUE:
  .\test-load-context.ps1 -context <contexte> [OPTIONS]

MODES DISPONIBLES:
  -mode asyncio    Mode original Test.Charge (défaut)
  -mode locust     Mode Locust pour tests de charge avancés

EXEMPLES:

  # Mode original (asyncio)
  .\test-load-context.ps1 -context andoc

  # Mode Locust avec interface web
  .\test-load-context.ps1 -context andoc -mode locust -locustUI

  # Test de stress Locust (100 utilisateurs, 5 minutes)
  .\test-load-context.ps1 -context andoc -mode locust -testType stress -users 100 -spawnRate 10 -runTime "5m" -headless

  # Test avec monitoring Prometheus
  .\test-load-context.ps1 -context andoc -mode locust -prometheusUrl "http://localhost:9091"

  # Test avec sorties CSV et HTML
  .\test-load-context.ps1 -context andoc -mode locust -headless -runTime "10m" -csvOutput "results" -htmlOutput "report.html"

TYPES DE TESTS LOCUST:
  standard  - Mix équilibré de tous les types d'utilisateurs
  stress    - Tests de charge lourde avec pics de trafic  
  smoke     - Tests légers de validation basique
  chaos     - Tests aléatoires et de gestion d'erreurs

MONITORING:
  -prometheusUrl   URL Prometheus Pushgateway
  -slackWebhook    Webhook Slack pour les alertes

Pour plus d'infos: docs/locust_integration.md
"@ -ForegroundColor White
    exit 0
}

# Exécution
try {
    Write-Host "ðŸš€ Lancement du test..." -ForegroundColor Green
    & $cmd[0] $cmd[1..($cmd.Length-1)]
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "âœ… Test terminé avec succÃ¨s!" -ForegroundColor Green
    } else {
        Write-Host "âŒ Test échoué avec le code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "âŒ Erreur lors de l'exécution: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    Write-Host "ðŸ Fin de session Test.Charge" -ForegroundColor Magenta
}
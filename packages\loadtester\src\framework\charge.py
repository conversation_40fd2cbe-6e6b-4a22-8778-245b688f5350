import sys
import logging
import re
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Dict, Any, List, Optional
from pathlib import Path

# These modules don't exist in standard Locust, commenting out
from .config import TestConfig, ConfigManager
from .report import ReportManager
from .monitoring import MonitoringManager

# # Create placeholder classes if needed
# class TestConfig:
#     """Placeholder for TestConfig"""
#     pass

# class ConfigManager:
#     """Placeholder for ConfigManager"""
#     pass

# class ReportManager:
#     """Placeholder for ReportManager"""
#     pass

# class MonitoringManager:
#     """Placeholder for MonitoringManager"""
#     pass

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from framework import HttpUser, task, between, events

    # StopUser might be in different locations depending on Locust version
    try:
        from locust.exception import StopUser
    except ImportError:
        try:
            from framework import StopUser
        except ImportError:
            # Create a placeholder if not available
            class StopUser(Exception):
                """Placeholder for StopUser exception"""

                pass

except ImportError:
    # Locust not installed, create placeholders
    class HttpUser:
        pass

    def task(f):
        return f

    def between(min_wait, max_wait):
        return lambda: min_wait

    class events:
        init = type("Event", (), {"add_listener": lambda f: f})()

    class StopUser(Exception):
        pass


# Import Test.Charge core components
try:
    from src.core.clients import HttpClient
    from src.core.timer import Timer
    from src.core.utils import validate_jwt, get_config_paths
except ImportError:
    # Try alternative import paths
    try:
        sys.path.insert(0, str(Path(__file__).parent.parent))
        from core.clients import HttpClient
        from core.timer import Timer
        from core.utils import validate_jwt, get_config_paths
    except ImportError:
        # Create placeholders if imports fail
        logger.warning("Test.Charge core components not available")

        class HttpClient:
            pass

        class Timer:
            pass

        def validate_jwt(*args, **kwargs):
            return False

        def get_config_paths(*args, **kwargs):
            return None, None


logger = logging.getLogger(__name__)


class AsyncBridge:
    """Bridge to run async Test.Charge code in sync Locust context"""

    def __init__(self):
        self._loop = None
        self._executor = ThreadPoolExecutor(max_workers=1)

    def run_async(self, coro):
        """Run an async coroutine in a sync context"""
        try:
            # Try to get existing event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, we need to run in a separate thread
                future = self._executor.submit(self._run_in_new_loop, coro)
                return future.result(timeout=30)  # 30 second timeout
            else:
                # Loop exists but not running, we can use it
                return loop.run_until_complete(coro)
        except RuntimeError:
            # No event loop exists, create one
            return asyncio.run(coro)

    def _run_in_new_loop(self, coro):
        """Run coroutine in a new event loop"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()

    def __del__(self):
        if self._executor:
            self._executor.shutdown(wait=False)


class TestChargeUser(HttpUser):
    """
    Enhanced Locust User that integrates Test.Charge core functionality
    """

    abstract = True
    wait_time = between(1, 3)

    def __init__(self, environment):
        super().__init__(environment)
        self.config: Optional[TestConfig] = None
        self.test_charge_client: Optional[HttpClient] = None
        self.timer = Timer()
        self.extracted_variables: Dict[str, Any] = {}
        self.user_session_id: Optional[str] = None
        self.async_bridge = AsyncBridge()

        # Get configuration from environment
        if hasattr(environment, "test_config"):
            self.config = environment.test_config

    def on_start(self):
        """Initialize the user with Test.Charge configuration"""
        try:
            if not self.config:
                logger.error("No configuration available for TestChargeUser")
                raise StopUser("Configuration not available")

            # Initialize Test.Charge timer
            self.timer.start_global()
            self.timer.set_thread_and_task_info(1, len(self.config.endpoints))

            # Initialize Test.Charge HTTP client
            self.test_charge_client = HttpClient(
                base_url=self.config.base_url,
                jwt_token=self.config.jwt_token,
                timer=self.timer,
                verify_ssl=self.config.verify_ssl,
                config=self._convert_config_to_dict(),
            )

            # Set up authentication headers for Locust client
            self.client.headers.update(
                {"X-CGPT-AUTHORIZATION": f"Bearer {self.config.jwt_token}"}
            )

            # Verify SSL setting
            self.client.verify = self.config.verify_ssl

            # Set timeouts
            self.client.timeout = (
                self.config.connection_timeout,
                self.config.read_timeout,
            )

            # Set host for Locust client
            if not self.host:
                self.host = self.config.base_url

            # Validate JWT token using async bridge
            if not self._should_skip_jwt_validation():
                try:
                    is_valid, message = self.async_bridge.run_async(
                        validate_jwt(
                            self.config.base_url,
                            self.config.jwt_token,
                            self.config.verify_ssl,
                        )
                    )
                    logger.info(f"JWT validation: {message}")
                    if not is_valid:
                        logger.error("JWT validation failed")
                        raise StopUser("JWT validation failed")
                except Exception as e:
                    logger.warning(f"JWT validation error: {e}")
                    # Continue anyway for development/testing

            logger.info(f"TestChargeUser initialized for {self.config.base_url}")

        except Exception as e:
            logger.error(f"Error initializing TestChargeUser: {e}")
            raise StopUser(f"Initialization failed: {e}")

    def on_stop(self):
        """Cleanup when user stops"""
        logger.info("TestChargeUser stopping")

        if self.timer:
            self.timer.end_global()
            # Print summary using async bridge
            try:
                self.async_bridge.run_async(
                    self.timer.print_summary(
                        show_detailed_errors=True, show_status_code_details=True
                    )
                )
            except Exception as e:
                logger.warning(f"Error printing timer summary: {e}")

        if self.test_charge_client:
            # Log any final statistics
            logger.info("Test.Charge client cleanup completed")

    def _should_skip_jwt_validation(self) -> bool:
        """Check if JWT validation should be skipped"""
        import os

        return os.environ.get("SKIP_JWT_VALIDATION", "").lower() == "true"

    def _convert_config_to_dict(self) -> Dict[str, Any]:
        """Convert TestConfig to dictionary for Test.Charge client"""
        if not self.config:
            return {}

        return {
            "timeout_connect": self.config.connection_timeout,
            "timeout_read": self.config.read_timeout,
            "timeout_write": self.config.read_timeout,
            "timeout_pool": 10.0,
            "debug_mode": False,
            "verbose_errors": True,
            "enhanced_logging": {
                "log_request_headers": False,
                "log_response_headers": False,
                "log_full_response_on_error": True,
            },
        }

    def execute_test_charge_sequence(self, endpoints: List[Dict[str, Any]]) -> bool:
        """
        Execute a Test.Charge-style sequence of endpoints with dependencies

        Args:
            endpoints: List of endpoint configurations

        Returns:
            bool: True if sequence completed successfully
        """
        try:
            # Reset extracted variables for this sequence
            self.extracted_variables = {}

            for i, endpoint_config in enumerate(endpoints, 1):
                # Check dependencies before execution
                depends_on = endpoint_config.get("depends_on")
                if depends_on:
                    logger.debug(
                        f"Endpoint {endpoint_config.get('id')} depends on {depends_on}"
                    )

                success = self.execute_endpoint(
                    endpoint_config, step=i, total_steps=len(endpoints)
                )
                if not success:
                    logger.warning(f"Endpoint {i} failed, stopping sequence")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error executing Test.Charge sequence: {e}")
            return False

    def execute_endpoint(
        self, endpoint_config: Dict[str, Any], step: int = 1, total_steps: int = 1
    ) -> bool:
        """
        Execute a single endpoint with Test.Charge functionality

        Args:
            endpoint_config: Endpoint configuration
            step: Current step number
            total_steps: Total number of steps

        Returns:
            bool: True if endpoint executed successfully
        """
        try:
            url = endpoint_config.get("url", "/")
            method = endpoint_config.get("method", "GET").upper()
            data = endpoint_config.get("data", {})
            params = endpoint_config.get("params", {})
            extract = endpoint_config.get("extract", [])
            endpoint_id = endpoint_config.get("id", url)
            depends_on = endpoint_config.get("depends_on")

            # Enhanced dependency processing with proper error handling
            if depends_on or self._has_variables(url, data, params):
                processed = self._process_dependencies_enhanced(
                    url, data, params, endpoint_id
                )
                if processed is None:
                    logger.error(f"Failed to process dependencies for {endpoint_id}")
                    return False
                url, data, params = processed

            # Prepare request name for Locust statistics
            request_name = endpoint_id or f"{method} {url}"

            # Make the request using Locust client for proper statistics
            response = None
            with self.client.request(
                method=method,
                url=url,
                json=data if method in ["POST", "PUT", "PATCH"] else None,
                params=params,
                name=request_name,
                catch_response=True,
            ) as locust_response:

                # Handle the response
                if locust_response.status_code >= 400:
                    locust_response.failure(f"HTTP {locust_response.status_code}")
                    logger.warning(
                        f"Request failed: {method} {url} -> {locust_response.status_code}"
                    )

                    # Log response content for debugging
                    try:
                        error_content = locust_response.text[:200]
                        logger.warning(f"Error response: {error_content}")
                    except:
                        pass

                    return False
                else:
                    locust_response.success()
                    response = locust_response

            # Extract data if configured
            if extract and response:
                self._extract_variables_enhanced(response, extract, endpoint_id)

            logger.debug(f"Successfully executed {method} {url} ({endpoint_id})")
            return True

        except Exception as e:
            logger.error(f"Error executing endpoint {endpoint_config.get('url')}: {e}")
            return False

    def _has_variables(
        self, url: str, data: Dict[str, Any], params: Dict[str, Any]
    ) -> bool:
        """Check if any of the components have variable placeholders"""

        def has_placeholder(obj):
            if isinstance(obj, str):
                return "{" in obj
            elif isinstance(obj, dict):
                return any(has_placeholder(v) for v in obj.values())
            elif isinstance(obj, list):
                return any(has_placeholder(item) for item in obj)
            return False

        return has_placeholder(url) or has_placeholder(data) or has_placeholder(params)

    def _process_dependencies_enhanced(
        self,
        url: str,
        data: Dict[str, Any],
        params: Dict[str, Any],
        endpoint_id: str = None,
    ) -> Optional[tuple]:
        """Enhanced dependency processing with proper error handling"""

        # Find all variables needed
        all_variables = set()
        all_variables.update(self._find_variables(url))
        all_variables.update(self._find_variables_in_dict(data))
        all_variables.update(self._find_variables_in_dict(params))

        # Check for missing variables
        missing_variables = [
            var for var in all_variables if var not in self.extracted_variables
        ]

        if missing_variables:
            logger.error(
                f"Missing required variables for {endpoint_id}: {missing_variables}"
            )
            logger.error(
                f"Available variables: {list(self.extracted_variables.keys())}"
            )
            return None

        try:
            # Process URL
            processed_url = self._substitute_variables(url)

            # Process data
            processed_data = self._substitute_variables_in_dict(data)

            # Process params
            processed_params = self._substitute_variables_in_dict(params)

            logger.debug(f"Variables substituted for {endpoint_id}")
            return processed_url, processed_data, processed_params

        except Exception as e:
            logger.error(f"Error processing dependencies for {endpoint_id}: {e}")
            return None

    def _find_variables(self, text: str) -> List[str]:
        """Find all variable placeholders in a string"""
        if not isinstance(text, str):
            return []

        var_pattern = r"\{([^{}]+)\}"
        return re.findall(var_pattern, text)

    def _find_variables_in_dict(self, obj: Any) -> List[str]:
        """Find all variable placeholders in a dictionary or other object"""
        variables = []

        if isinstance(obj, str):
            variables.extend(self._find_variables(obj))
        elif isinstance(obj, dict):
            for value in obj.values():
                variables.extend(self._find_variables_in_dict(value))
        elif isinstance(obj, list):
            for item in obj:
                variables.extend(self._find_variables_in_dict(item))

        return variables

    def _substitute_variables(self, text: str) -> str:
        """Substitute variables in a string"""
        if not isinstance(text, str) or "{" not in text:
            return text

        try:
            return text.format(**self.extracted_variables)
        except KeyError as e:
            logger.error(f"Variable substitution failed: {e}")
            raise

    def _substitute_variables_in_dict(self, obj: Any) -> Any:
        """Recursively substitute variables in a dictionary or other object"""
        if isinstance(obj, str):
            return self._substitute_variables(obj)
        elif isinstance(obj, dict):
            result = {}
            for key, value in obj.items():
                result[key] = self._substitute_variables_in_dict(value)
            return result
        elif isinstance(obj, list):
            return [self._substitute_variables_in_dict(item) for item in obj]
        else:
            return obj

    def _has_variables(
        self, url: str, data: Dict[str, Any], params: Dict[str, Any]
    ) -> bool:
        """Check if any of the inputs contain variable placeholders"""
        all_variables = set()
        all_variables.update(self._find_variables(url))
        all_variables.update(self._find_variables_in_dict(data))
        all_variables.update(self._find_variables_in_dict(params))
        return len(all_variables) > 0

    def _extract_variables_enhanced(
        self, response, extract_config: List[Dict[str, str]], endpoint_id: str
    ):
        """Enhanced variable extraction with better error handling"""
        try:
            response_data = response.json()
        except Exception as e:
            logger.warning(f"Could not parse JSON response from {endpoint_id}: {e}")
            return

        if not isinstance(response_data, dict):
            logger.warning(f"Response data is not a dictionary for {endpoint_id}")
            return

        for extraction in extract_config:
            if (
                not isinstance(extraction, dict)
                or "key" not in extraction
                or "value_from" not in extraction
            ):
                logger.warning(f"Invalid extraction config: {extraction}")
                continue

            key = extraction["key"]
            value_from = extraction["value_from"]

            if value_from in response_data:
                extracted_value = response_data[value_from]
                self.extracted_variables[key] = extracted_value
                logger.info(f"Extracted {key} = {extracted_value} from {endpoint_id}")
            else:
                logger.warning(
                    f"Key '{value_from}' not found in response from {endpoint_id}"
                )
                logger.debug(f"Available keys: {list(response_data.keys())}")


class ConfiguredTestUser(TestChargeUser):
    """
    Test user that automatically runs configured endpoint sequences
    """

    weight = 1

    @task
    def run_configured_endpoints(self):
        """Run the configured endpoint sequence"""
        if not self.config or not self.config.endpoints:
            logger.warning("No endpoints configured")
            return

        # Execute the endpoint sequence with enhanced dependency management
        success = self.execute_test_charge_sequence(self.config.endpoints)

        if not success:
            logger.warning("Endpoint sequence execution failed")


class ScenarioBasedUser(TestChargeUser):
    """
    Test user that runs specific scenarios from configuration
    """

    weight = 1

    def __init__(self, environment):
        super().__init__(environment)
        self.scenarios: List[Dict[str, Any]] = []

    def on_start(self):
        """Initialize with scenario configuration"""
        super().on_start()

        # Load scenarios from configuration
        if self.config and hasattr(self.config, "test_data"):
            self.scenarios = self.config.test_data.get("scenarios", [])

        if not self.scenarios:
            logger.warning("No scenarios configured for ScenarioBasedUser")

    @task
    def run_random_scenario(self):
        """Execute a random scenario"""
        if not self.scenarios:
            return

        import random

        scenario = random.choice(self.scenarios)

        scenario_name = scenario.get("name", "unnamed_scenario")
        endpoints = scenario.get("endpoints", [])

        logger.debug(f"Executing scenario: {scenario_name}")
        self.execute_test_charge_sequence(endpoints)


class TestChargeRunner:
    """
    Main runner class that sets up Locust environment with Test.Charge configuration
    """

    def __init__(self, config_context: str = None, config_environment: str = None):
        self.config_manager = ConfigManager()
        self.config_context = config_context
        self.config_environment = config_environment
        self.config: Optional[TestConfig] = None
        self.report_manager: Optional[ReportManager] = None
        self.monitoring_manager: Optional[MonitoringManager] = None

    async def initialize(self) -> bool:
        """Initialize the Test.Charge runner"""
        try:
            # Load configuration
            self.config = self.config_manager.load_config(
                context=self.config_context, environment=self.config_environment
            )

            # Validate configuration
            errors = self.config_manager.validate_config(self.config)
            if errors:
                logger.error(f"Configuration validation failed: {errors}")
                return False

            # Validate JWT if not skipped and validate_jwt is available
            if not self._should_skip_jwt_validation() and validate_jwt:
                # For Locust compatibility, we'll do basic JWT validation without async calls
                from framework.utils import JWTValidator

                token_info = JWTValidator.get_token_info(self.config.jwt_token)
                jwt_message = JWTValidator.format_token_info(token_info)

                logger.info(jwt_message)

                if not token_info.get("valid", False):
                    logger.error("JWT validation failed")
                    return False
                elif token_info.get("expires_soon", False):
                    logger.warning("JWT token expires soon")

            # Initialize reporting
            self.report_manager = ReportManager(
                report_dir=self.config.report_dir,
                slow_request_threshold=self.config.slow_request_threshold,
            )

            # Initialize monitoring if enabled
            if self.config.enable_monitoring:
                self.monitoring_manager = MonitoringManager()
                # Add monitoring integrations based on configuration
                self._setup_monitoring()

            logger.info("Test.Charge runner initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Error initializing Test.Charge runner: {e}")
            return False

    def _should_skip_jwt_validation(self) -> bool:
        """Check if JWT validation should be skipped"""
        import os

        return os.environ.get("SKIP_JWT_VALIDATION", "").lower() in ["true", "1", "yes"]

    def _setup_monitoring(self):
        """Setup monitoring integrations based on configuration"""
        if not self.monitoring_manager:
            return

        # Add monitoring integrations based on configuration
        # This would be configured through environment variables or config
        import os

        # Prometheus integration
        prometheus_url = os.environ.get("PROMETHEUS_PUSHGATEWAY_URL")
        if prometheus_url:
            from .monitoring import PrometheusIntegration

            self.monitoring_manager.add_integration(
                PrometheusIntegration(prometheus_url)
            )

        # Slack integration
        slack_webhook = os.environ.get("SLACK_WEBHOOK_URL")
        if slack_webhook:
            from .monitoring import SlackIntegration

            self.monitoring_manager.add_alert_integration(
                SlackIntegration(slack_webhook)
            )

    def setup_locust_environment(self, environment):
        """Setup Locust environment with Test.Charge configuration"""
        # Store configuration in environment for user access
        environment.test_config = self.config
        environment.test_charge_runner = self

        logger.info(f"Locust environment configured for {self.config.base_url}")

        # Setup event listeners
        self._setup_event_listeners(environment)

    def _setup_event_listeners(self, environment):
        """Setup enhanced event listeners"""

        @events.test_start.add_listener
        def on_test_start(environment, **kwargs):
            """Handle test start"""
            logger.info("Test.Charge load test starting with Locust")

            # Send metrics to monitoring systems
            if self.monitoring_manager:
                metrics = {
                    "test_started": 1,
                    "configured_users": self.config.num_users,
                    "spawn_rate": self.config.spawn_rate,
                }
                self.monitoring_manager.send_metrics(metrics)

        @events.test_stop.add_listener
        def on_test_stop(environment, **kwargs):
            """Handle test stop"""
            logger.info("Test.Charge load test completed")

            # Send final metrics
            if self.monitoring_manager:
                total_requests = sum(
                    stats.num_requests for stats in environment.stats.entries.values()
                )
                total_failures = sum(
                    stats.num_failures for stats in environment.stats.entries.values()
                )

                metrics = {
                    "test_completed": 1,
                    "total_requests": total_requests,
                    "total_failures": total_failures,
                    "success_rate": (
                        (total_requests - total_failures) / total_requests * 100
                        if total_requests > 0
                        else 0
                    ),
                }
                self.monitoring_manager.send_metrics(metrics)

        @events.request.add_listener
        def on_request_complete(
            request_type,
            name,
            response_time,
            response_length,
            response,
            context,
            exception,
            **kwargs,
        ):
            """Enhanced request logging"""

            # Send real-time metrics to monitoring systems
            if self.monitoring_manager:
                metrics = {
                    "response_time": response_time,
                    "response_length": response_length,
                    "success": (
                        1
                        if not exception
                        and (not response or response.status_code < 400)
                        else 0
                    ),
                }

                # Don't block on monitoring - fire and forget
                try:
                    self.monitoring_manager.send_metrics(metrics)
                except:
                    pass  # Don't let monitoring failures affect tests


# Global runner instance for use in locustfile
_runner_instance: Optional[TestChargeRunner] = None


def get_runner() -> TestChargeRunner:
    """Get or create the global runner instance"""
    global _runner_instance

    if _runner_instance is None:
        import os

        context = os.environ.get("CONFIG_CONTEXT", "andoc")
        environment = os.environ.get("CONFIG_ENVIRONMENT")
        _runner_instance = TestChargeRunner(context, environment)

    return _runner_instance


def setup_locust_environment(environment):
    """Setup function to be called from locustfile"""
    runner = get_runner()

    # Initialize synchronously for Locust compatibility
    import asyncio

    try:
        # Try to get existing event loop
        loop = asyncio.get_event_loop()
    except RuntimeError:
        # Create new event loop if none exists
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    # Initialize runner
    if not loop.run_until_complete(runner.initialize()):
        raise RuntimeError("Failed to initialize Test.Charge runner")

    # Setup environment
    runner.setup_locust_environment(environment)

    return runner

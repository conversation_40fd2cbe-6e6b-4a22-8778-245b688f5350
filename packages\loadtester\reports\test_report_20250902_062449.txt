Fichier de contexte utilisé : configs/localapi_simple.json

===== RéSUMé DU TEST =====
Temps d'exécution total: 2.7739s
Nombre total de requètes: 45
Nombre de threads: 5
Nombre de tÃ¢ches par thread: 9
Nombre total de tÃ¢ches: 45

--- STATISTIQUES PAR ENDPOINT ---

health:
  Nombre: 5
  Min: 0.2673s
  Max: 0.3273s
  Moyenne: 0.2818s
  Médiane: 0.2718s

login:
  Nombre: 5
  Min: 0.2738s
  Max: 0.3079s
  Moyenne: 0.2930s
  Médiane: 0.2896s

validate:
  Nombre: 5
  Min: 0.2646s
  Max: 0.2673s
  Moyenne: 0.2653s
  Médiane: 0.2650s

profile:
  Nombre: 5
  Min: 0.2538s
  Max: 0.2664s
  Moyenne: 0.2607s
  Médiane: 0.2642s

create_item:
  Nombre: 5
  Min: 0.3066s
  Max: 0.3431s
  Moyenne: 0.3220s
  Médiane: 0.3113s

get_item:
  Nombre: 5
  Min: 0.2645s
  Max: 0.2663s
  Moyenne: 0.2651s
  Médiane: 0.2650s

list_items:
  Nombre: 5
  Min: 0.2949s
  Max: 0.3275s
  Moyenne: 0.3035s
  Médiane: 0.2972s

search:
  Nombre: 5
  Min: 0.3886s
  Max: 0.4828s
  Moyenne: 0.4440s
  Médiane: 0.4614s

analytics:
  Nombre: 5
  Min: 0.2542s
  Max: 0.2691s
  Moyenne: 0.2651s
  Médiane: 0.2676s

--- STATISTIQUES PAR THREAD ---
Thread 4: 9 requètes en 2.6827s
Thread 1: 9 requètes en 2.6848s
Thread 0: 9 requètes en 2.7694s
Thread 2: 9 requètes en 2.7162s
Thread 3: 9 requètes en 2.6497s

--- STATISTIQUES DES CODES HTTP ---

SuccÃ¨s (2xx):
  200: 45 occurrences

Fichier de contexte utilisé : configs/localapi_simple.json

===== RéSUMé DU TEST =====
Temps d'exécution total: 2.8599s
Nombre total de requètes: 45
Nombre de threads: 5
Nombre de tÃ¢ches par thread: 9
Nombre total de tÃ¢ches: 45

--- STATISTIQUES PAR ENDPOINT ---

health:
  Nombre: 5
  Min: 0.2714s
  Max: 0.3310s
  Moyenne: 0.2866s
  Médiane: 0.2773s

login:
  Nombre: 5
  Min: 0.3178s
  Max: 0.3249s
  Moyenne: 0.3198s
  Médiane: 0.3189s

validate:
  Nombre: 5
  Min: 0.2649s
  Max: 0.2711s
  Moyenne: 0.2678s
  Médiane: 0.2673s

profile:
  Nombre: 5
  Min: 0.2565s
  Max: 0.2576s
  Moyenne: 0.2573s
  Médiane: 0.2575s

create_item:
  Nombre: 5
  Min: 0.3542s
  Max: 0.3635s
  Moyenne: 0.3592s
  Médiane: 0.3593s

get_item:
  Nombre: 5
  Min: 0.2569s
  Max: 0.2676s
  Moyenne: 0.2616s
  Médiane: 0.2615s

list_items:
  Nombre: 5
  Min: 0.2934s
  Max: 0.3264s
  Moyenne: 0.3125s
  Médiane: 0.3102s

search:
  Nombre: 5
  Min: 0.4069s
  Max: 0.4989s
  Moyenne: 0.4556s
  Médiane: 0.4532s

analytics:
  Nombre: 5
  Min: 0.2551s
  Max: 0.2691s
  Moyenne: 0.2637s
  Médiane: 0.2684s

--- STATISTIQUES PAR THREAD ---
Thread 3: 9 requètes en 2.7729s
Thread 1: 9 requètes en 2.7099s
Thread 0: 9 requètes en 2.8568s
Thread 2: 9 requètes en 2.8043s
Thread 4: 9 requètes en 2.7759s

--- STATISTIQUES DES CODES HTTP ---

SuccÃ¨s (2xx):
  200: 45 occurrences

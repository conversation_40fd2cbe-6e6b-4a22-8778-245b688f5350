"""
Test Orchestrator for Locust Load Testing Framework
==================================================

Coordinates test execution, manages configurations, and provides
a unified interface for running different types of load tests.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

from .config import ConfigManager, TestConfig
from .report import ReportManager

logger = logging.getLogger(__name__)


class TestType(Enum):
    """Enumeration of available test types"""

    LOAD = "load"
    STRESS = "stress"
    SPIKE = "spike"
    ENDURANCE = "endurance"
    SCALABILITY = "scalability"
    BASELINE = "baseline"
    SMOKE = "smoke"


@dataclass
class TestResult:
    """Container for test execution results"""

    test_type: TestType
    success: bool
    duration_seconds: float
    total_requests: int
    failed_requests: int
    success_rate: float
    avg_response_time: float
    peak_users: int
    error_message: Optional[str] = None
    report_files: List[str] = None

    def __post_init__(self):
        if self.report_files is None:
            self.report_files = []


class TestOrchestrator:
    """Orchestrates load test execution with different scenarios"""

    def __init__(self, base_dir: Optional[Path] = None):
        self.base_dir = base_dir or Path.cwd()
        self.config_manager = ConfigManager(self.base_dir)
        self.locust_files_dir = self.base_dir / "src" / "locust_tests"
        self.reports_dir = self.base_dir / "reports"

        # Ensure directories exist
        self.locust_files_dir.mkdir(parents=True, exist_ok=True)
        self.reports_dir.mkdir(exist_ok=True)

        logger.info(
            f"TestOrchestrator initialized with base directory: {self.base_dir}"
        )

    def run_test(
        self,
        test_type: TestType,
        context: Optional[str] = None,
        environment: Optional[str] = None,
        config_overrides: Optional[Dict[str, Any]] = None,
    ) -> TestResult:
        """
        Run a specific type of load test

        Args:
            test_type: Type of test to run
            context: Context-specific configuration
            environment: Environment configuration
            config_overrides: Runtime configuration overrides

        Returns:
            TestResult: Results of the test execution
        """
        logger.info(f"Starting {test_type.value} test")

        try:
            # Load configuration
            config = self.config_manager.load_config(
                context=context, environment=environment, overrides=config_overrides
            )

            # Validate configuration
            errors = self.config_manager.validate_config(config)
            if errors:
                error_msg = f"Configuration validation failed: {', '.join(errors)}"
                logger.error(error_msg)
                return TestResult(
                    test_type=test_type,
                    success=False,
                    duration_seconds=0,
                    total_requests=0,
                    failed_requests=0,
                    success_rate=0,
                    avg_response_time=0,
                    peak_users=0,
                    error_message=error_msg,
                )

            # Get test-specific configuration
            test_config = self._get_test_specific_config(test_type, config)

            # Get locust file for test type
            locust_file = self._get_locust_file(test_type)

            # Run the test
            result = self._execute_locust_test(test_type, locust_file, test_config)

            logger.info(
                f"{test_type.value} test completed: {'SUCCESS' if result.success else 'FAILED'}"
            )
            return result

        except Exception as e:
            error_msg = f"Error running {test_type.value} test: {str(e)}"
            logger.error(error_msg)
            return TestResult(
                test_type=test_type,
                success=False,
                duration_seconds=0,
                total_requests=0,
                failed_requests=0,
                success_rate=0,
                avg_response_time=0,
                peak_users=0,
                error_message=error_msg,
            )

    def run_test_suite(
        self, contexts: List[str], test_types: Optional[List[TestType]] = None
    ) -> List[TestResult]:
        """
        Run a suite of tests across multiple contexts

        Args:
            contexts: List of contexts to test
            test_types: List of test types to run (default: all)

        Returns:
            List[TestResult]: Results from all tests
        """
        if test_types is None:
            test_types = [TestType.SMOKE, TestType.LOAD, TestType.STRESS]

        results = []

        for context in contexts:
            for test_type in test_types:
                logger.info(f"Running {test_type.value} test for context: {context}")
                result = self.run_test(test_type, context=context)
                results.append(result)

                # Stop if critical test fails
                if not result.success and test_type in [
                    TestType.SMOKE,
                    TestType.BASELINE,
                ]:
                    logger.error(
                        f"Critical test failed for {context}, skipping remaining tests"
                    )
                    break

        return results

    def _get_test_specific_config(
        self, test_type: TestType, base_config: TestConfig
    ) -> TestConfig:
        """Get test-type specific configuration adjustments"""
        config = base_config

        if test_type == TestType.SMOKE:
            config.num_users = min(5, config.num_users)
            config.run_time = "30s"
            config.spawn_rate = 1.0

        elif test_type == TestType.BASELINE:
            config.num_users = 1
            config.run_time = "1m"
            config.spawn_rate = 1.0

        elif test_type == TestType.LOAD:
            # Use configured values
            pass

        elif test_type == TestType.STRESS:
            config.num_users = int(config.num_users * 1.5)  # 150% of normal load
            config.run_time = "10m"

        elif test_type == TestType.SPIKE:
            config.spawn_rate = config.num_users  # Spawn all users immediately
            config.run_time = "5m"

        elif test_type == TestType.ENDURANCE:
            config.run_time = "30m"  # Long duration

        elif test_type == TestType.SCALABILITY:
            # This would typically involve multiple test runs with increasing users
            pass

        return config

    def _get_locust_file(self, test_type: TestType) -> Path:
        """Get the appropriate locust file for the test type"""
        locust_file_map = {
            TestType.LOAD: "load_test.py",
            TestType.STRESS: "stress_test.py",
            TestType.SPIKE: "spike_test.py",
            TestType.ENDURANCE: "endurance_test.py",
            TestType.SCALABILITY: "scalability_test.py",
            TestType.BASELINE: "baseline_test.py",
            TestType.SMOKE: "smoke_test.py",
        }

        filename = locust_file_map.get(test_type, "load_test.py")
        locust_file = self.locust_files_dir / filename

        # If specific file doesn't exist, use the generic one
        if not locust_file.exists():
            generic_file = self.locust_files_dir / "generic_test.py"
            if generic_file.exists():
                return generic_file
            else:
                # Create a basic locust file
                self._create_basic_locust_file(locust_file)

        return locust_file

    def _execute_locust_test(
        self, test_type: TestType, locust_file: Path, config: TestConfig
    ) -> TestResult:
        """Execute the locust test with the given configuration"""
        import time

        start_time = time.time()

        try:
            # Create temporary config file
            temp_config_file = self.base_dir / f"temp_config_{test_type.value}.json"
            self.config_manager.save_config(config, temp_config_file)

            # Prepare locust command
            cmd = [
                sys.executable,
                "-m",
                "locust",
                "-f",
                str(locust_file),
                "--host",
                config.base_url,
                "--users",
                str(config.num_users),
                "--spawn-rate",
                str(config.spawn_rate),
                "--run-time",
                config.run_time,
                "--headless",
                "--html",
                str(self.reports_dir / f"{test_type.value}_report.html"),
                "--csv",
                str(self.reports_dir / f"{test_type.value}_results"),
            ]

            # Set environment variables for the test
            env = os.environ.copy()
            env["CONFIG_PATH"] = str(temp_config_file)
            env["JWT_TOKEN"] = config.jwt_token
            env["VERIFY_SSL"] = str(config.verify_ssl).lower()

            # Execute the test
            logger.info(f"Executing command: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                env=env,
                timeout=self._get_test_timeout(config.run_time),
            )

            duration = time.time() - start_time

            # Parse results from output
            test_result = self._parse_locust_output(
                test_type, result.stdout, result.stderr, duration
            )

            # Clean up temporary config
            if temp_config_file.exists():
                temp_config_file.unlink()

            return test_result

        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            return TestResult(
                test_type=test_type,
                success=False,
                duration_seconds=duration,
                total_requests=0,
                failed_requests=0,
                success_rate=0,
                avg_response_time=0,
                peak_users=0,
                error_message="Test timed out",
            )
        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                test_type=test_type,
                success=False,
                duration_seconds=duration,
                total_requests=0,
                failed_requests=0,
                success_rate=0,
                avg_response_time=0,
                peak_users=0,
                error_message=str(e),
            )

    def _parse_locust_output(
        self, test_type: TestType, stdout: str, stderr: str, duration: float
    ) -> TestResult:
        """Parse locust output to extract test results"""
        # This is a simplified parser - in production you might want more robust parsing
        total_requests = 0
        failed_requests = 0
        avg_response_time = 0
        peak_users = 0

        try:
            # Look for summary statistics in stdout
            lines = stdout.split("\n")
            for line in lines:
                if "Total requests" in line:
                    total_requests = int(line.split(":")[-1].strip())
                elif "Failed requests" in line:
                    failed_requests = int(line.split(":")[-1].strip())
                elif "Average response time" in line:
                    avg_response_time = float(
                        line.split(":")[-1].strip().replace("ms", "")
                    )
                elif "Peak users" in line:
                    peak_users = int(line.split(":")[-1].strip())
        except (ValueError, IndexError):
            logger.warning("Could not parse all statistics from locust output")

        success_rate = (
            (total_requests - failed_requests) / total_requests * 100
            if total_requests > 0
            else 0
        )

        # Consider test successful if success rate > 95% and no critical errors
        success = success_rate > 95.0 and "Error" not in stderr

        return TestResult(
            test_type=test_type,
            success=success,
            duration_seconds=duration,
            total_requests=total_requests,
            failed_requests=failed_requests,
            success_rate=success_rate,
            avg_response_time=avg_response_time,
            peak_users=peak_users,
            error_message=stderr if stderr and not success else None,
        )

    def _get_test_timeout(self, run_time: str) -> int:
        """Convert run time string to timeout in seconds"""
        # Simple parser for run time (e.g., "5m", "30s", "1h")
        if run_time.endswith("s"):
            return int(run_time[:-1]) + 60  # Add 1 minute buffer
        elif run_time.endswith("m"):
            return int(run_time[:-1]) * 60 + 120  # Add 2 minutes buffer
        elif run_time.endswith("h"):
            return int(run_time[:-1]) * 3600 + 300  # Add 5 minutes buffer
        else:
            return 600  # Default 10 minutes

    def _create_basic_locust_file(self, file_path: Path):
        """Create a basic locust file if none exists"""
        basic_content = """
from locust import HttpUser, task, between
from src.locust_framework import AuthenticatedUser
import os
import json

class BasicLoadTestUser(AuthenticatedUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        super().on_start()
        # Load endpoints from config
        config_path = os.environ.get("CONFIG_PATH", "config.global.json")
        with open(config_path, 'r') as f:
            config = json.load(f)
        self.endpoints = config.get("endpoints", [])
    
    @task
    def test_endpoints(self):
        for endpoint in self.endpoints:
            url = endpoint.get("url", "/")
            method = endpoint.get("method", "GET").upper()
            
            if method == "GET":
                self.authenticated_request("GET", url)
            elif method == "POST":
                data = endpoint.get("data", {})
                self.authenticated_request("POST", url, json=data)
"""

        with open(file_path, "w") as f:
            f.write(basic_content)

        logger.info(f"Created basic locust file: {file_path}")

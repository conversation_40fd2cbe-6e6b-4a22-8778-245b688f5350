Fichier de contexte utilisé : configs/localapi_simple.json

===== RéSUMé DU TEST =====
Temps d'exécution total: 2.8611s
Nombre total de requètes: 45
Nombre de threads: 5
Nombre de tÃ¢ches par thread: 9
Nombre total de tÃ¢ches: 45

--- STATISTIQUES PAR ENDPOINT ---

health:
  Nombre: 5
  Min: 0.2705s
  Max: 0.3159s
  Moyenne: 0.2809s
  Médiane: 0.2727s

login:
  Nombre: 5
  Min: 0.2974s
  Max: 0.3265s
  Moyenne: 0.3095s
  Médiane: 0.3111s

validate:
  Nombre: 5
  Min: 0.2572s
  Max: 0.2690s
  Moyenne: 0.2642s
  Médiane: 0.2685s

profile:
  Nombre: 5
  Min: 0.2628s
  Max: 0.2645s
  Moyenne: 0.2634s
  Médiane: 0.2632s

create_item:
  Nombre: 5
  Min: 0.3217s
  Max: 0.3652s
  Moyenne: 0.3485s
  Médiane: 0.3536s

get_item:
  Nombre: 5
  Min: 0.2622s
  Max: 0.2656s
  Moyenne: 0.2639s
  Médiane: 0.2635s

list_items:
  Nombre: 5
  Min: 0.2965s
  Max: 0.3322s
  Moyenne: 0.3107s
  Médiane: 0.3105s

search:
  Nombre: 5
  Min: 0.4010s
  Max: 0.5125s
  Moyenne: 0.4529s
  Médiane: 0.4515s

analytics:
  Nombre: 5
  Min: 0.2554s
  Max: 0.2686s
  Moyenne: 0.2658s
  Médiane: 0.2684s

--- STATISTIQUES PAR THREAD ---
Thread 0: 9 requètes en 2.7818s
Thread 3: 9 requètes en 2.7806s
Thread 1: 9 requètes en 2.6879s
Thread 2: 9 requètes en 2.8124s
Thread 4: 9 requètes en 2.7363s

--- STATISTIQUES DES CODES HTTP ---

SuccÃ¨s (2xx):
  200: 45 occurrences

import json
import time
import base64
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class SessionData:
    """Container for session data"""

    session_id: Optional[str] = None
    context_data: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_used: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class RequestInfo:
    """Information about a request"""

    method: str
    url: str
    response_time: float
    status_code: int
    error: Optional[str] = None
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


class JWTValidator:
    """JWT token validation utilities"""

    @staticmethod
    def decode_jwt_payload(token: str) -> Dict[str, Any]:
        """
        Decode JWT payload without verification (for info extraction only)

        Args:
            token: JWT token string

        Returns:
            Dict containing the payload data
        """
        try:
            # Remove 'Bearer ' prefix if present
            if token.startswith("Bearer "):
                token = token[7:]

            # Split token parts
            parts = token.split(".")
            if len(parts) != 3:
                raise ValueError("Invalid JWT format")

            # Decode payload (second part)
            payload_encoded = parts[1]

            # Add padding if needed
            padding = 4 - len(payload_encoded) % 4
            if padding != 4:
                payload_encoded += "=" * padding

            # Decode base64
            payload_bytes = base64.urlsafe_b64decode(payload_encoded)
            payload = json.loads(payload_bytes.decode("utf-8"))

            return payload

        except Exception as e:
            logger.error(f"Error decoding JWT payload: {e}")
            return {}

    @staticmethod
    def get_token_info(token: str) -> Dict[str, Any]:
        """
        Extract information from JWT token

        Args:
            token: JWT token string

        Returns:
            Dict containing token information
        """
        payload = JWTValidator.decode_jwt_payload(token)

        if not payload:
            return {"valid": False, "error": "Could not decode token"}

        current_time = time.time()

        # Check expiration
        exp = payload.get("exp")
        expired = False
        expires_soon = False

        if exp:
            expired = current_time > exp
            expires_soon = (
                not expired and (exp - current_time) < 3600
            )  # Less than 1 hour

        return {
            "valid": not expired,
            "expired": expired,
            "expires_soon": expires_soon,
            "exp": exp,
            "exp_datetime": (
                datetime.fromtimestamp(exp, tz=timezone.utc) if exp else None
            ),
            "iat": payload.get("iat"),
            "iss": payload.get("iss"),
            "sub": payload.get("sub"),
            "name": payload.get("name"),
            "email": payload.get("email"),
            "payload": payload,
        }

    @staticmethod
    def format_token_info(token_info: Dict[str, Any]) -> str:
        """Format token information as a readable string"""
        if not token_info.get("valid"):
            if token_info.get("expired"):
                exp_time = token_info.get("exp_datetime")
                if exp_time:
                    return f"âŒ JWT token expired at {exp_time.strftime('%Y-%m-%d %H:%M:%S UTC')}"
                else:
                    return "âŒ JWT token is expired"
            else:
                return (
                    f"âŒ Invalid JWT token: {token_info.get('error', 'Unknown error')}"
                )

        lines = ["âœ… JWT token is valid"]

        if token_info.get("name"):
            lines.append(f"   â€¢ User: {token_info['name']}")

        if token_info.get("email"):
            lines.append(f"   â€¢ Email: {token_info['email']}")

        exp_time = token_info.get("exp_datetime")
        if exp_time:
            lines.append(
                f"   â€¢ Expires: {exp_time.strftime('%Y-%m-%d %H:%M:%S UTC')}"
            )

            if token_info.get("expires_soon"):
                lines.append("   âš ï¸ Warning: Token expires within 1 hour")

        return "\n".join(lines)


class SessionManager:
    """Manages user sessions for load testing"""

    def __init__(self, base_url: str, jwt_token: str, verify_ssl: bool = True):
        self.base_url = base_url.rstrip("/")
        self.jwt_token = jwt_token
        self.verify_ssl = verify_ssl
        self.session_data = SessionData()

    def get_session_headers(self) -> Dict[str, str]:
        """Get headers with session information"""
        headers = {}

        if self.session_data.session_id:
            headers["X-Session-ID"] = self.session_data.session_id

        # Add any context-specific headers
        for key, value in self.session_data.context_data.items():
            if isinstance(value, str) and key.startswith("header_"):
                header_name = key[7:]  # Remove 'header_' prefix
                headers[header_name] = value

        return headers

    def update_session_data(self, data: Dict[str, Any]):
        """Update session data with new information"""
        self.session_data.context_data.update(data)
        self.session_data.last_used = datetime.now(timezone.utc)

    def is_session_valid(self) -> bool:
        """Check if session is still valid (basic check)"""
        if not self.session_data.session_id:
            return False

        # Session is valid if used within last hour
        time_diff = datetime.now(timezone.utc) - self.session_data.last_used
        return time_diff.total_seconds() < 3600


class RequestTracker:
    """Tracks and analyzes request statistics"""

    def __init__(self):
        self.requests: List[RequestInfo] = []
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.status_counts: Dict[int, int] = defaultdict(int)
        self.method_counts: Dict[str, int] = defaultdict(int)

    def record_request(
        self,
        method: str,
        url: str,
        response_time: float,
        status_code: int,
        error: Optional[str] = None,
    ):
        """Record a request"""
        request_info = RequestInfo(
            method=method,
            url=url,
            response_time=response_time,
            status_code=status_code,
            error=error,
        )

        self.requests.append(request_info)
        self.status_counts[status_code] += 1
        self.method_counts[method] += 1

        if error:
            self.error_counts[error] += 1

    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics"""
        if not self.requests:
            return {}

        response_times = [r.response_time for r in self.requests]

        return {
            "total_requests": len(self.requests),
            "avg_response_time": sum(response_times) / len(response_times),
            "min_response_time": min(response_times),
            "max_response_time": max(response_times),
            "error_rate": len([r for r in self.requests if r.error])
            / len(self.requests)
            * 100,
            "status_codes": dict(self.status_counts),
            "methods": dict(self.method_counts),
            "top_errors": dict(
                sorted(self.error_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            ),
        }

    def get_slow_requests(self, threshold: float = 5000.0) -> List[RequestInfo]:
        """Get requests slower than threshold"""
        return [r for r in self.requests if r.response_time > threshold]

    def clear(self):
        """Clear all recorded data"""
        self.requests.clear()
        self.error_counts.clear()
        self.status_counts.clear()
        self.method_counts.clear()


class PerformanceAnalyzer:
    """Analyzes performance patterns and trends"""

    def __init__(self):
        self.request_tracker = RequestTracker()

    def analyze_response_time_trend(self, window_size: int = 10) -> List[float]:
        """Analyze response time trend using moving average"""
        if len(self.request_tracker.requests) < window_size:
            return []

        moving_averages = []
        requests = self.request_tracker.requests

        for i in range(window_size, len(requests) + 1):
            window = requests[i - window_size : i]
            avg = sum(r.response_time for r in window) / window_size
            moving_averages.append(avg)

        return moving_averages

    def detect_performance_degradation(self, threshold_increase: float = 50.0) -> bool:
        """Detect if performance is degrading"""
        trend = self.analyze_response_time_trend()

        if len(trend) < 2:
            return False

        # Compare last few measurements with earlier ones
        recent_avg = sum(trend[-5:]) / min(5, len(trend))
        earlier_avg = sum(trend[:5]) / min(5, len(trend))

        increase_percentage = ((recent_avg - earlier_avg) / earlier_avg) * 100

        return increase_percentage > threshold_increase

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a comprehensive performance summary"""
        stats = self.request_tracker.get_statistics()

        if not stats:
            return {"status": "no_data"}

        # Determine performance status
        error_rate = stats.get("error_rate", 0)
        avg_response_time = stats.get("avg_response_time", 0)

        if error_rate > 10 or avg_response_time > 10000:
            status = "poor"
        elif error_rate > 5 or avg_response_time > 5000:
            status = "concerning"
        elif error_rate > 1 or avg_response_time > 2000:
            status = "acceptable"
        else:
            status = "good"

        return {
            "status": status,
            "degradation_detected": self.detect_performance_degradation(),
            "statistics": stats,
            "slow_requests_count": len(self.request_tracker.get_slow_requests()),
            "recommendations": self._get_recommendations(stats),
        }

    def _get_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """Generate performance recommendations"""
        recommendations = []

        error_rate = stats.get("error_rate", 0)
        avg_response_time = stats.get("avg_response_time", 0)

        if error_rate > 5:
            recommendations.append(
                "High error rate detected - check server logs and capacity"
            )

        if avg_response_time > 5000:
            recommendations.append(
                "High response times - consider scaling or optimization"
            )

        status_codes = stats.get("status_codes", {})
        if status_codes.get(429, 0) > 0:
            recommendations.append(
                "Rate limiting detected - consider reducing load or implementing backoff"
            )

        if (
            status_codes.get(500, 0)
            + status_codes.get(502, 0)
            + status_codes.get(503, 0)
            > 0
        ):
            recommendations.append("Server errors detected - check application health")

        return recommendations


# Convenience functions for common operations
def validate_and_format_jwt(token: str) -> Tuple[bool, str]:
    """
    Validate JWT token and return formatted message

    Args:
        token: JWT token string

    Returns:
        Tuple of (is_valid, formatted_message)
    """
    token_info = JWTValidator.get_token_info(token)
    message = JWTValidator.format_token_info(token_info)
    return token_info.get("valid", False), message


def create_session_manager(
    base_url: str, jwt_token: str, verify_ssl: bool = True
) -> SessionManager:
    """Create and return a configured session manager"""
    return SessionManager(base_url, jwt_token, verify_ssl)


def create_request_tracker() -> RequestTracker:
    """Create and return a new request tracker"""
    return RequestTracker()


def create_performance_analyzer() -> PerformanceAnalyzer:
    """Create and return a new performance analyzer"""
    return PerformanceAnalyzer()

import time
import logging
import requests
import json
import threading
import psutil
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from collections import deque

from src.locust.core import events

logger = logging.getLogger(__name__)


@dataclass
class Alert:
    """Alert data structure"""
    name: str
    message: str
    severity: str
    timestamp: datetime
    metrics: Dict[str, Any]
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class MonitoringIntegration(ABC):
    """Abstract base class for monitoring integrations"""
    
    @abstractmethod
    def send_metrics(self, metrics: Dict[str, Any]) -> bool:
        """Send metrics to monitoring system"""
        pass
    
    @abstractmethod
    def send_alert(self, alert: Alert) -> bool:
        """Send alert to monitoring system"""
        pass


class PrometheusIntegration(MonitoringIntegration):
    """Integration with Prometheus monitoring system"""
    
    def __init__(self, pushgateway_url: str, job_name: str = "locust_load_test"):
        self.pushgateway_url = pushgateway_url.rstrip('/')
        self.job_name = job_name
        self.instance_id = f"locust_{int(time.time())}"
        
    def send_metrics(self, metrics: Dict[str, Any]) -> bool:
        """Send metrics to Prometheus Pushgateway"""
        try:
            # Convert metrics to Prometheus format
            prometheus_metrics = self._convert_to_prometheus_format(metrics)
            
            # Send to pushgateway
            url = f"{self.pushgateway_url}/metrics/job/{self.job_name}/instance/{self.instance_id}"
            
            response = requests.post(
                url,
                data=prometheus_metrics,
                headers={'Content-Type': 'text/plain'},
                timeout=10
            )
            
            if response.status_code == 200:
                logger.debug("Metrics sent to Prometheus successfully")
                return True
            else:
                logger.error(f"Failed to send metrics to Prometheus: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending metrics to Prometheus: {e}")
            return False
    
    def send_alert(self, alert: Alert) -> bool:
        """Send alert as metric to Prometheus"""
        try:
            alert_metrics = {
                f"locust_alert_{alert.name}": 1 if not alert.resolved else 0,
                "locust_alert_severity": self._severity_to_number(alert.severity)
            }
            
            return self.send_metrics(alert_metrics)
            
        except Exception as e:
            logger.error(f"Error sending alert to Prometheus: {e}")
            return False
    
    def _convert_to_prometheus_format(self, metrics: Dict[str, Any]) -> str:
        """Convert metrics dictionary to Prometheus format"""
        lines = []
        timestamp = int(time.time() * 1000)  # Prometheus expects milliseconds
        
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                # Clean metric name for Prometheus
                metric_name = f"locust_{key.replace('.', '_').replace('-', '_')}"
                lines.append(f"{metric_name} {value} {timestamp}")
        
        return '\n'.join(lines)
    
    def _severity_to_number(self, severity: str) -> int:
        """Convert severity string to number"""
        severity_map = {
            "info": 1,
            "warning": 2,
            "error": 3,
            "critical": 4
        }
        return severity_map.get(severity.lower(), 1)


class InfluxDBIntegration(MonitoringIntegration):
    """Integration with InfluxDB time series database"""
    
    def __init__(self, url: str, database: str, username: str = None, password: str = None):
        self.url = url.rstrip('/')
        self.database = database
        self.auth = (username, password) if username and password else None
        
    def send_metrics(self, metrics: Dict[str, Any]) -> bool:
        """Send metrics to InfluxDB"""
        try:
            # Convert to InfluxDB line protocol
            lines = self._convert_to_influx_format(metrics)
            
            # Send to InfluxDB
            url = f"{self.url}/write?db={self.database}"
            
            response = requests.post(
                url,
                data='\n'.join(lines),
                auth=self.auth,
                timeout=10
            )
            
            if response.status_code == 204:
                logger.debug("Metrics sent to InfluxDB successfully")
                return True
            else:
                logger.error(f"Failed to send metrics to InfluxDB: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending metrics to InfluxDB: {e}")
            return False
    
    def send_alert(self, alert: Alert) -> bool:
        """Send alert to InfluxDB"""
        try:
            alert_data = {
                "alert_active": 1 if not alert.resolved else 0,
                "alert_severity": self._severity_to_number(alert.severity),
                **alert.metrics
            }
            
            return self.send_metrics(alert_data)
            
        except Exception as e:
            logger.error(f"Error sending alert to InfluxDB: {e}")
            return False
    
    def _convert_to_influx_format(self, metrics: Dict[str, Any]) -> List[str]:
        """Convert metrics to InfluxDB line protocol"""
        lines = []
        timestamp = int(time.time() * 1000000000)  # InfluxDB expects nanoseconds
        
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                line = f"locust_metrics,test=load_test {key}={value} {timestamp}"
                lines.append(line)
        
        return lines
    
    def _severity_to_number(self, severity: str) -> int:
        """Convert severity string to number"""
        severity_map = {
            "info": 1,
            "warning": 2,
            "error": 3,
            "critical": 4
        }
        return severity_map.get(severity.lower(), 1)


class SlackIntegration:
    """Integration with Slack for alert notifications"""
    
    def __init__(self, webhook_url: str, channel: str = None):
        self.webhook_url = webhook_url
        self.channel = channel
        
    def send_alert(self, alert: Alert) -> bool:
        """Send alert to Slack"""
        try:
            # Choose emoji based on severity
            emoji_map = {
                "info": "â„¹ï¸",
                "warning": "âš ï¸",
                "error": "âŒ",
                "critical": "ðŸš¨"
            }
            
            emoji = emoji_map.get(alert.severity.lower(), "ðŸ“Š")
            
            # Create Slack message
            message = {
                "text": f"{emoji} Load Test Alert: {alert.name}",
                "attachments": [
                    {
                        "color": self._severity_to_color(alert.severity),
                        "fields": [
                            {
                                "title": "Message",
                                "value": alert.message,
                                "short": False
                            },
                            {
                                "title": "Severity",
                                "value": alert.severity.upper(),
                                "short": True
                            },
                            {
                                "title": "Time",
                                "value": alert.timestamp.strftime("%Y-%m-%d %H:%M:%S UTC"),
                                "short": True
                            }
                        ]
                    }
                ]
            }
            
            if self.channel:
                message["channel"] = self.channel
            
            # Add metrics as fields if available
            if alert.metrics:
                for key, value in list(alert.metrics.items())[:5]:  # Limit to 5 metrics
                    message["attachments"][0]["fields"].append({
                        "title": key.replace('_', ' ').title(),
                        "value": str(value),
                        "short": True
                    })
            
            # Send to Slack
            response = requests.post(
                self.webhook_url,
                json=message,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.debug("Alert sent to Slack successfully")
                return True
            else:
                logger.error(f"Failed to send alert to Slack: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending alert to Slack: {e}")
            return False
    
    def _severity_to_color(self, severity: str) -> str:
        """Convert severity to Slack color"""
        color_map = {
            "info": "good",
            "warning": "warning", 
            "error": "danger",
            "critical": "danger"
        }
        return color_map.get(severity.lower(), "good")


class EmailIntegration:
    """Integration for email alert notifications"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, 
                 password: str, from_email: str, to_emails: List[str]):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.from_email = from_email
        self.to_emails = to_emails
        
    def send_alert(self, alert: Alert) -> bool:
        """Send alert via email"""
        try:
            import smtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = f"Load Test Alert: {alert.name} [{alert.severity.upper()}]"
            
            # Create email body
            body = f"""
Load Test Alert Notification

Alert: {alert.name}
Severity: {alert.severity.upper()}
Message: {alert.message}
Time: {alert.timestamp.strftime("%Y-%m-%d %H:%M:%S UTC")}
Status: {'RESOLVED' if alert.resolved else 'ACTIVE'}

Metrics:
"""
            
            for key, value in alert.metrics.items():
                body += f"  {key}: {value}\n"
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.username, self.password)
            
            text = msg.as_string()
            server.sendmail(self.from_email, self.to_emails, text)
            server.quit()
            
            logger.debug("Alert sent via email successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error sending alert via email: {e}")
            return False


class WebhookIntegration:
    """Generic webhook integration for custom systems"""
    
    def __init__(self, webhook_url: str, headers: Dict[str, str] = None):
        self.webhook_url = webhook_url
        self.headers = headers or {}
        
    def send_metrics(self, metrics: Dict[str, Any]) -> bool:
        """Send metrics via webhook"""
        try:
            payload = {
                "type": "metrics",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "data": metrics
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code in [200, 201, 202]:
                logger.debug("Metrics sent via webhook successfully")
                return True
            else:
                logger.error(f"Failed to send metrics via webhook: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending metrics via webhook: {e}")
            return False
    
    def send_alert(self, alert: Alert) -> bool:
        """Send alert via webhook"""
        try:
            payload = {
                "type": "alert",
                "alert": {
                    "name": alert.name,
                    "message": alert.message,
                    "severity": alert.severity,
                    "timestamp": alert.timestamp.isoformat(),
                    "resolved": alert.resolved,
                    "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None,
                    "metrics": alert.metrics
                }
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code in [200, 201, 202]:
                logger.debug("Alert sent via webhook successfully")
                return True
            else:
                logger.error(f"Failed to send alert via webhook: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending alert via webhook: {e}")
            return False


class MonitoringManager:
    """Manages multiple monitoring integrations"""
    
    def __init__(self):
        self.integrations: List[MonitoringIntegration] = []
        self.alert_integrations: List[Any] = []  # Can include non-MonitoringIntegration classes
        
    def add_integration(self, integration: MonitoringIntegration):
        """Add a monitoring integration"""
        self.integrations.append(integration)
        logger.info(f"Added monitoring integration: {type(integration).__name__}")
        
    def add_alert_integration(self, integration: Any):
        """Add an alert integration"""
        self.alert_integrations.append(integration)
        logger.info(f"Added alert integration: {type(integration).__name__}")
        
    def send_metrics(self, metrics: Dict[str, Any]) -> Dict[str, bool]:
        """Send metrics to all integrations"""
        results = {}
        
        for integration in self.integrations:
            try:
                success = integration.send_metrics(metrics)
                results[type(integration).__name__] = success
            except Exception as e:
                logger.error(f"Error in {type(integration).__name__}: {e}")
                results[type(integration).__name__] = False
                
        return results
        
    def send_alert(self, alert: Alert) -> Dict[str, bool]:
        """Send alert to all alert integrations"""
        results = {}
        
        # Send to monitoring integrations that support alerts
        for integration in self.integrations:
            if hasattr(integration, 'send_alert'):
                try:
                    success = integration.send_alert(alert)
                    results[type(integration).__name__] = success
                except Exception as e:
                    logger.error(f"Error in {type(integration).__name__}: {e}")
                    results[type(integration).__name__] = False
        
        # Send to dedicated alert integrations
        for integration in self.alert_integrations:
            try:
                success = integration.send_alert(alert)
                results[type(integration).__name__] = success
            except Exception as e:
                logger.error(f"Error in {type(integration).__name__}: {e}")
                results[type(integration).__name__] = False
                
        return results



@dataclass
class MetricSnapshot:
    """Snapshot of metrics at a point in time"""
    timestamp: datetime
    active_users: int
    requests_per_second: float
    failures_per_second: float
    avg_response_time: float
    cpu_percent: float
    memory_percent: float
    network_io: Dict[str, int] = field(default_factory=dict)
    custom_metrics: Dict[str, Any] = field(default_factory=dict)


class AlertManager:
    """Manages alerts and notifications during load testing"""
    
    def __init__(self):
        self.alert_rules = []
        self.alert_callbacks = []
        self.active_alerts = {}
        
    def add_alert_rule(self, name: str, condition: Callable[[MetricSnapshot], bool], 
                      message: str, severity: str = "warning"):
        """Add an alert rule"""
        self.alert_rules.append({
            "name": name,
            "condition": condition,
            "message": message,
            "severity": severity
        })
        
    def add_alert_callback(self, callback: Callable[[str, str, str], None]):
        """Add callback for alert notifications"""
        self.alert_callbacks.append(callback)
        
    def check_alerts(self, snapshot: MetricSnapshot):
        """Check all alert rules against current metrics"""
        for rule in self.alert_rules:
            rule_name = rule["name"]
            
            try:
                if rule["condition"](snapshot):
                    # Alert condition met
                    if rule_name not in self.active_alerts:
                        # New alert
                        self.active_alerts[rule_name] = {
                            "triggered_at": snapshot.timestamp,
                            "count": 1
                        }
                        self._trigger_alert(rule_name, rule["message"], rule["severity"])
                    else:
                        # Existing alert
                        self.active_alerts[rule_name]["count"] += 1
                else:
                    # Alert condition not met
                    if rule_name in self.active_alerts:
                        # Alert resolved
                        del self.active_alerts[rule_name]
                        self._resolve_alert(rule_name)
                        
            except Exception as e:
                logger.error(f"Error checking alert rule {rule_name}: {e}")
    
    def _trigger_alert(self, name: str, message: str, severity: str):
        """Trigger an alert"""
        logger.warning(f"ALERT [{severity.upper()}] {name}: {message}")
        
        for callback in self.alert_callbacks:
            try:
                callback(name, message, severity)
            except Exception as e:
                logger.error(f"Error in alert callback: {e}")
    
    def _resolve_alert(self, name: str):
        """Resolve an alert"""
        logger.info(f"ALERT RESOLVED: {name}")


class RealTimeMonitor:
    """Real-time monitoring system for load tests"""
    
    def __init__(self, update_interval: float = 5.0, history_size: int = 1000):
        self.update_interval = update_interval
        self.history_size = history_size
        
        # Metrics storage
        self.metrics_history = deque(maxlen=history_size)
        self.current_metrics = {}
        
        # Monitoring state
        self.monitoring = False
        self.monitor_thread = None
        
        # Alert management
        self.alert_manager = AlertManager()
        
        # System monitoring
        self.system_monitor = SystemMonitor()
        
        # Setup default alert rules
        self._setup_default_alerts()
        
        # Setup event listeners
        self._setup_event_listeners()
        
        logger.info("RealTimeMonitor initialized")
    
    def start_monitoring(self):
        """Start real-time monitoring"""
        if self.monitoring:
            logger.warning("Monitoring already started")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("Real-time monitoring started")
    
    def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
        
        logger.info("Real-time monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                # Collect current metrics
                snapshot = self._collect_metrics()
                
                # Store in history
                self.metrics_history.append(snapshot)
                
                # Check alerts
                self.alert_manager.check_alerts(snapshot)
                
                # Log current status
                self._log_status(snapshot)
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.update_interval)
    
    def _collect_metrics(self) -> MetricSnapshot:
        """Collect current metrics snapshot"""
        # System metrics
        system_metrics = self.system_monitor.get_current_metrics()
        
        # Locust metrics (from current_metrics updated by event listeners)
        locust_metrics = self.current_metrics.copy()
        
        snapshot = MetricSnapshot(
            timestamp=datetime.now(timezone.utc),
            active_users=locust_metrics.get("active_users", 0),
            requests_per_second=locust_metrics.get("requests_per_second", 0.0),
            failures_per_second=locust_metrics.get("failures_per_second", 0.0),
            avg_response_time=locust_metrics.get("avg_response_time", 0.0),
            cpu_percent=system_metrics["cpu_percent"],
            memory_percent=system_metrics["memory_percent"],
            network_io=system_metrics["network_io"],
            custom_metrics=locust_metrics.get("custom_metrics", {})
        )
        
        return snapshot
    
    def _log_status(self, snapshot: MetricSnapshot):
        """Log current status"""
        logger.info(
            f"ðŸ“Š Users: {snapshot.active_users} | "
            f"RPS: {snapshot.requests_per_second:.1f} | "
            f"Failures/s: {snapshot.failures_per_second:.1f} | "
            f"Avg RT: {snapshot.avg_response_time:.0f}ms | "
            f"CPU: {snapshot.cpu_percent:.1f}% | "
            f"Memory: {snapshot.memory_percent:.1f}%"
        )
    
    def _setup_event_listeners(self):
        """Setup Locust event listeners for metrics collection"""
        
        @events.request.add_listener
        def on_request(request_type, name, response_time, response_length, 
                      response, context, exception, **kwargs):
            # Update request metrics
            self.current_metrics["last_response_time"] = response_time
            
        @events.user_add.add_listener
        def on_user_add(user_instance, **kwargs):
            self.current_metrics["active_users"] = self.current_metrics.get("active_users", 0) + 1
            
        @events.user_remove.add_listener
        def on_user_remove(user_instance, **kwargs):
            self.current_metrics["active_users"] = max(0, self.current_metrics.get("active_users", 1) - 1)
    
    def _setup_default_alerts(self):
        """Setup default alert rules"""
        
        # High error rate alert
        self.alert_manager.add_alert_rule(
            name="high_error_rate",
            condition=lambda s: s.failures_per_second > s.requests_per_second * 0.1,  # >10% error rate
            message="Error rate exceeds 10%",
            severity="critical"
        )
        
        # High response time alert
        self.alert_manager.add_alert_rule(
            name="high_response_time",
            condition=lambda s: s.avg_response_time > 5000,  # >5 seconds
            message="Average response time exceeds 5 seconds",
            severity="warning"
        )
        
        # High CPU usage alert
        self.alert_manager.add_alert_rule(
            name="high_cpu_usage",
            condition=lambda s: s.cpu_percent > 90,
            message="CPU usage exceeds 90%",
            severity="warning"
        )
        
        # High memory usage alert
        self.alert_manager.add_alert_rule(
            name="high_memory_usage",
            condition=lambda s: s.memory_percent > 95,
            message="Memory usage exceeds 95%",
            severity="critical"
        )
        
        # Low throughput alert
        self.alert_manager.add_alert_rule(
            name="low_throughput",
            condition=lambda s: s.active_users > 10 and s.requests_per_second < s.active_users * 0.1,
            message="Throughput is unexpectedly low",
            severity="warning"
        )
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        if not self.metrics_history:
            return {"status": "no_data"}
        
        latest = self.metrics_history[-1]
        
        return {
            "status": "active" if self.monitoring else "stopped",
            "timestamp": latest.timestamp.isoformat(),
            "active_users": latest.active_users,
            "requests_per_second": latest.requests_per_second,
            "failures_per_second": latest.failures_per_second,
            "avg_response_time": latest.avg_response_time,
            "cpu_percent": latest.cpu_percent,
            "memory_percent": latest.memory_percent,
            "active_alerts": list(self.alert_manager.active_alerts.keys()),
            "metrics_count": len(self.metrics_history)
        }
    
    def get_metrics_history(self, minutes: int = 10) -> List[Dict[str, Any]]:
        """Get metrics history for the last N minutes"""
        cutoff_time = datetime.now(timezone.utc).timestamp() - (minutes * 60)
        
        filtered_metrics = [
            {
                "timestamp": m.timestamp.isoformat(),
                "active_users": m.active_users,
                "requests_per_second": m.requests_per_second,
                "failures_per_second": m.failures_per_second,
                "avg_response_time": m.avg_response_time,
                "cpu_percent": m.cpu_percent,
                "memory_percent": m.memory_percent
            }
            for m in self.metrics_history
            if m.timestamp.timestamp() > cutoff_time
        ]
        
        return filtered_metrics
    
    def export_metrics(self, file_path: str):
        """Export metrics history to JSON file"""
        metrics_data = {
            "export_time": datetime.now(timezone.utc).isoformat(),
            "total_snapshots": len(self.metrics_history),
            "metrics": [
                {
                    "timestamp": m.timestamp.isoformat(),
                    "active_users": m.active_users,
                    "requests_per_second": m.requests_per_second,
                    "failures_per_second": m.failures_per_second,
                    "avg_response_time": m.avg_response_time,
                    "cpu_percent": m.cpu_percent,
                    "memory_percent": m.memory_percent,
                    "network_io": m.network_io,
                    "custom_metrics": m.custom_metrics
                }
                for m in self.metrics_history
            ]
        }
        
        with open(file_path, 'w') as f:
            json.dump(metrics_data, f, indent=2)
        
        logger.info(f"Metrics exported to {file_path}")


class SystemMonitor:
    """System resource monitoring"""
    
    def __init__(self):
        self.process = psutil.Process()
        
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current system metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            
            # Network I/O
            network_io = psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {}
            
            return {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_gb": memory.available / (1024**3),
                "network_io": network_io,
                "disk_io": disk_io,
                "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else [0, 0, 0]
            }
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            return {
                "cpu_percent": 0,
                "memory_percent": 0,
                "memory_available_gb": 0,
                "network_io": {},
                "disk_io": {},
                "load_average": [0, 0, 0]
            }
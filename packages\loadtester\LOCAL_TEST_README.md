# Local Testing Guide for Load Tester

This guide demonstrates how to test the Load Tester application against a local FastAPI server, providing a complete end-to-end testing environment.

## Quick Start

### 1. Start the Test API Server

```bash
# Terminal 1
python test_api.py
```

The server will start on `http://localhost:8001` with:
- **Username**: `testuser`
- **Password**: `testpass123`
- **API Docs**: http://localhost:8001/docs

### 2. Run Load Tests

```bash
# Terminal 2 - Interactive Demo
python run_demo.py

# Or run directly
python -m src.main -c configs/localapi_simple.json --skip-jwt-validation
```

## Components

### Test API Server (`test_api.py`)

A fully-featured FastAPI application that simulates a production API with:

#### Authentication Endpoints
- `POST /auth/login` - Login with username/password, returns JWT token
- `GET /auth/validate` - Validate JWT token

#### User Management
- `GET /api/user/profile` - Get user profile (requires auth)
- `PUT /api/user/profile` - Update user profile (requires auth)

#### CRUD Operations
- `POST /api/items` - Create new item
- `GET /api/items` - List all items with pagination
- `GET /api/items/{id}` - Get specific item
- `PUT /api/items/{id}` - Update item
- `DELETE /api/items/{id}` - Delete item

#### Advanced Features
- `POST /api/upload` - File upload endpoint
- `POST /api/search` - Search with complex filters
- `POST /api/batch/items` - Batch create items
- `GET /api/analytics/summary` - Analytics dashboard

#### Stress Test Endpoints
- `GET /api/stress/cpu` - CPU intensive operation
- `GET /api/stress/memory` - Memory allocation test

### Configuration Files

#### `configs/localapi_simple.json`
Basic configuration with essential endpoints for testing core functionality:
- Authentication flow
- CRUD operations
- Search and analytics

#### `configs/localapi.json`
Comprehensive configuration with:
- All available endpoints
- File upload testing
- Stress test scenarios
- Weighted endpoint distribution
- Template variables for dynamic data

### Demo Script (`run_demo.py`)

Interactive script that:
1. Checks if the API server is running
2. Offers multiple test modes:
   - **Basic asyncio** - Original test mode
   - **Smoke test** - Quick validation (3 users, 10 seconds)
   - **Stress test** - High load testing (10 users, 30 seconds)
3. Displays test results and metrics

## Test Scenarios

### 1. Basic Functionality Test
Tests authentication and basic CRUD operations:
```bash
python -m src.main -c configs/localapi_simple.json --skip-jwt-validation
```

### 2. Smoke Test
Quick validation with minimal load:
```bash
python -m src.main -c configs/localapi_simple.json \
  --mode asyncio \
  --test-type smoke \
  --asyncio-users 3 \
  --asyncio-duration 10 \
  --skip-jwt-validation
```

### 3. Standard Load Test
Moderate load for performance baseline:
```bash
python -m src.main -c configs/localapi.json \
  --mode asyncio \
  --test-type standard \
  --asyncio-users 20 \
  --asyncio-duration 60 \
  --skip-jwt-validation
```

### 4. Stress Test
High load to find system limits:
```bash
python -m src.main -c configs/localapi.json \
  --mode asyncio \
  --test-type stress \
  --asyncio-users 50 \
  --asyncio-duration 300 \
  --ramp-up-time 30 \
  --skip-jwt-validation
```

### 5. Chaos Test
Unpredictable load patterns:
```bash
python -m src.main -c configs/localapi.json \
  --mode asyncio \
  --test-type chaos \
  --asyncio-users 30 \
  --asyncio-duration 180 \
  --skip-jwt-validation
```

### 6. Locust Mode with Web UI
Interactive web-based testing:
```bash
python -m src.main -c configs/localapi.json \
  --mode locust \
  --locust-ui
```
Then open http://localhost:8089 in your browser.

## Test Flow Example

The load tester executes the following flow:

1. **Health Check** - Verify server is responsive
2. **Authentication** - Login to get JWT token
3. **Token Validation** - Verify token is valid
4. **User Operations** - Get and update profile
5. **Item Creation** - Create new items with extracted ID
6. **Item Operations** - Get, list, update created items
7. **Search** - Test search functionality
8. **Analytics** - Retrieve analytics summary
9. **Cleanup** - Delete created items (optional)

## Understanding the Results

### Console Output
```
===== RÉSUMÉ DU TEST =====
Temps d'exécution total: 2.2069s
Nombre total de requêtes: 40
Nombre de threads: 5
```

### Performance Metrics
- **Response Times**: Min, Max, Average, Median per endpoint
- **Throughput**: Requests per second
- **Error Rate**: Failed requests percentage
- **Thread Statistics**: Performance per concurrent user

### Output Files
- `reports/test_report_*.txt` - Detailed test reports
- `logs/errors_*.log` - Error logs for debugging
- `results_*.csv` - CSV data for analysis (enhanced mode)
- `results_*.json` - JSON metrics (enhanced mode)

## Troubleshooting

### Port Already in Use
If port 8001 is busy, modify the port in:
1. `test_api.py` - Line 333: `uvicorn.run(app, host="0.0.0.0", port=8001)`
2. `configs/localapi*.json` - Line 2: `"base_url": "http://localhost:8001"`

### JWT Token Issues
- Use `--skip-jwt-validation` flag to bypass JWT validation
- Ensure the test server is running before starting tests
- Check that authentication endpoints return valid tokens

### Encoding Errors
If you see Unicode/emoji encoding errors on Windows:
```bash
# Set console to UTF-8
chcp 65001

# Or set environment variable
set PYTHONIOENCODING=utf-8
```

### High Error Rate
Common causes:
- Server not running
- Incorrect configuration
- Too many concurrent users for system resources
- Network/firewall blocking connections

## Advanced Usage

### Custom Test Data
Modify `configs/localapi.json` to add custom test data:
```json
{
  "data": {
    "name": "Custom Item {{random_number}}",
    "description": "Created at {{timestamp}}",
    "price": 99.99
  }
}
```

### File Upload Testing
Place test files in the project directory and reference in config:
```json
{
  "files": {
    "file": "test_file.txt"
  }
}
```

### Monitoring
For real-time monitoring during tests:
```bash
# In a separate terminal
watch -n 1 "tail -20 logs/errors_*.log"
```

### Distributed Testing (Locust)
For high-scale testing with multiple machines:
```bash
# Master node
python -m src.main -c configs/localapi.json \
  --mode locust \
  --master

# Worker nodes
python -m src.main -c configs/localapi.json \
  --mode locust \
  --worker \
  --master-host <master-ip>
```

## Performance Tips

1. **System Limits**: Increase file descriptors and network limits for high concurrency
2. **Resource Monitoring**: Watch CPU, memory, and network during tests
3. **Gradual Ramp-up**: Use `--ramp-up-time` to gradually increase load
4. **Think Time**: Add realistic delays between requests with `--think-time-min`
5. **Connection Pooling**: The tool uses connection pooling for efficiency

## Example Test Results

### Successful Test Output
```
Thread 0: Étape 1/9 - health
Thread 0 - /health (ID: health): 0.0234s (Status: 200)
Thread 0: Étape 2/9 - login
Thread 0 - /auth/login (ID: login): 0.0456s (Status: 200)
Token extracted: eyJhbGciOiJIUzI1NiIs...
```

### Performance Metrics
```
--- STATISTIQUES PAR ENDPOINT ---
login:
  Nombre: 50
  Min: 0.0234s
  Max: 0.0890s
  Moyenne: 0.0456s
  Médiane: 0.0423s
  Percentile 95: 0.0812s
  Percentile 99: 0.0875s
```

## Next Steps

1. **Customize Endpoints**: Add your own API endpoints to `test_api.py`
2. **Create Scenarios**: Design test scenarios in configuration files
3. **Analyze Results**: Use generated CSV/JSON files for detailed analysis
4. **Scale Testing**: Progress from smoke to stress tests
5. **CI/CD Integration**: Incorporate into your build pipeline

## Support

For issues or questions:
1. Check the main README.md for general documentation
2. Review error logs in `logs/` directory
3. Verify server is running with `curl http://localhost:8001/health`
4. Ensure all dependencies are installed: `pip install -e ".[all]"`

## License

This test environment is part of the Load Tester project and follows the same MIT license.
Fichier de contexte utilisé : configs/localapi_simple.json

===== RéSUMé DU TEST =====
Temps d'exécution total: 2.8508s
Nombre total de requètes: 45
Nombre de threads: 5
Nombre de tÃ¢ches par thread: 9
Nombre total de tÃ¢ches: 45

--- STATISTIQUES PAR ENDPOINT ---

health:
  Nombre: 5
  Min: 0.2740s
  Max: 0.3230s
  Moyenne: 0.2862s
  Médiane: 0.2780s

login:
  Nombre: 5
  Min: 0.2878s
  Max: 0.3133s
  Moyenne: 0.2977s
  Médiane: 0.2990s

validate:
  Nombre: 5
  Min: 0.2609s
  Max: 0.2710s
  Moyenne: 0.2656s
  Médiane: 0.2671s

profile:
  Nombre: 5
  Min: 0.2631s
  Max: 0.2690s
  Moyenne: 0.2655s
  Médiane: 0.2649s

create_item:
  Nombre: 5
  Min: 0.2975s
  Max: 0.3773s
  Moyenne: 0.3457s
  Médiane: 0.3658s

get_item:
  Nombre: 5
  Min: 0.2614s
  Max: 0.2753s
  Moyenne: 0.2666s
  Médiane: 0.2625s

list_items:
  Nombre: 5
  Min: 0.2937s
  Max: 0.3121s
  Moyenne: 0.3050s
  Médiane: 0.3106s

search:
  Nombre: 5
  Min: 0.3733s
  Max: 0.5148s
  Moyenne: 0.4538s
  Médiane: 0.4665s

analytics:
  Nombre: 5
  Min: 0.2665s
  Max: 0.2689s
  Moyenne: 0.2676s
  Médiane: 0.2670s

--- STATISTIQUES PAR THREAD ---
Thread 3: 9 requètes en 2.7712s
Thread 1: 9 requètes en 2.8060s
Thread 2: 9 requètes en 2.6971s
Thread 4: 9 requètes en 2.7549s
Thread 0: 9 requètes en 2.7395s

--- STATISTIQUES DES CODES HTTP ---

SuccÃ¨s (2xx):
  200: 45 occurrences

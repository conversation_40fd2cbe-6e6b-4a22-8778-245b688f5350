import datetime
import json
import logging
import re
import time
import asyncio
import statistics
import aiofiles
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

@dataclass
class RequestMetric:
    """Individual request metric"""
    timestamp: float
    endpoint: str
    method: str
    response_time: float  # milliseconds
    status_code: int
    response_length: int = 0
    success: bool = True
    error_message: Optional[str] = None
    thread_id: Optional[int] = None
    user_id: Optional[str] = None
    worker_id: Optional[int] = None
    test_type: Optional[str] = None


@dataclass
class EndpointStats:
    """Statistics for a specific endpoint"""
    endpoint: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    response_times: List[float] = field(default_factory=list)
    error_counts: Dict[int, int] = field(default_factory=dict)
    
    @property
    def success_rate(self) -> float:
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    @property
    def average_response_time(self) -> float:
        if self.total_requests == 0:
            return 0.0
        return self.total_response_time / self.total_requests
    
    @property
    def percentiles(self) -> Dict[str, float]:
        if not self.response_times:
            return {'p50': 0.0, 'p90': 0.0, 'p95': 0.0, 'p99': 0.0}
        
        sorted_times = sorted(self.response_times)
        return {
            'p50': self._percentile(sorted_times, 50),
            'p90': self._percentile(sorted_times, 90), 
            'p95': self._percentile(sorted_times, 95),
            'p99': self._percentile(sorted_times, 99)
        }
    
    def _percentile(self, sorted_data: List[float], percentile: int) -> float:
        if not sorted_data:
            return 0.0
        index = (percentile / 100) * (len(sorted_data) - 1)
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))


class PerformanceMetricsCollector:
    """Advanced performance metrics collector with real-time analytics"""
    
    def __init__(self, buffer_size: int = 10000):
        self.buffer_size = buffer_size
        self.metrics_buffer: deque = deque(maxlen=buffer_size)
        self.endpoint_stats: Dict[str, EndpointStats] = {}
        self.real_time_metrics: Dict[str, Any] = {}
        
        # Time-based metrics
        self.start_time = time.time()
        self.last_update = time.time()
        
        # Rate tracking
        self.request_counts = deque(maxlen=60)  # Last 60 seconds
        self.error_counts = deque(maxlen=60)
        
        # Performance thresholds
        self.thresholds = {
            'response_time_warning': 1000,  # ms
            'response_time_critical': 3000,  # ms
            'error_rate_warning': 5,  # percentage
            'error_rate_critical': 10,  # percentage
            'throughput_minimum': 1.0  # requests/second
        }
        
        # Alerting
        self.alerts: List[Dict[str, Any]] = []
        self.alert_cooldown: Dict[str, float] = {}
        
    def add_metric(self, metric: RequestMetric):
        """Add a new metric to the collection"""
        
        # Add to buffer
        self.metrics_buffer.append(metric)
        
        # Update endpoint statistics
        endpoint_key = f"{metric.method} {metric.endpoint}"
        
        # Create EndpointStats if it doesn't exist
        if endpoint_key not in self.endpoint_stats:
            self.endpoint_stats[endpoint_key] = EndpointStats(endpoint=endpoint_key)
        
        stats = self.endpoint_stats[endpoint_key]
            
        stats.total_requests += 1
        stats.total_response_time += metric.response_time
        stats.response_times.append(metric.response_time)
        
        if metric.success:
            stats.successful_requests += 1
        else:
            stats.failed_requests += 1
            stats.error_counts[metric.status_code] = stats.error_counts.get(metric.status_code, 0) + 1
        
        # Keep response times list manageable
        if len(stats.response_times) > 1000:
            stats.response_times = stats.response_times[-1000:]
        
        # Update real-time metrics
        self._update_real_time_metrics()
        
        # Check for alerts
        self._check_performance_alerts(metric, stats)
    
    def _update_real_time_metrics(self):
        """Update real-time performance metrics"""
        
        current_time = time.time()
        
        # Calculate throughput (requests per second)
        recent_metrics = [
            m for m in self.metrics_buffer 
            if current_time - m.timestamp <= 60  # Last 60 seconds
        ]
        
        if recent_metrics:
            throughput = len(recent_metrics) / min(60, current_time - self.start_time)
            error_count = sum(1 for m in recent_metrics if not m.success)
            error_rate = (error_count / len(recent_metrics)) * 100 if recent_metrics else 0
            
            avg_response_time = statistics.mean([m.response_time for m in recent_metrics])
            
            self.real_time_metrics = {
                'current_throughput': throughput,
                'current_error_rate': error_rate,
                'current_avg_response_time': avg_response_time,
                'active_requests': len([m for m in recent_metrics if current_time - m.timestamp <= 5]),
                'total_requests': len(self.metrics_buffer),
                'uptime_seconds': current_time - self.start_time
            }
        
        self.last_update = current_time
    
    def _check_performance_alerts(self, metric: RequestMetric, stats: EndpointStats):
        """Check for performance issues and generate alerts"""
        
        current_time = time.time()
        alert_key_prefix = f"{metric.endpoint}_{metric.method}"
        
        # Response time alerts
        if metric.response_time > self.thresholds['response_time_critical']:
            self._create_alert(
                'response_time_critical',
                f"Critical response time: {metric.response_time:.0f}ms on {metric.endpoint}",
                {'endpoint': metric.endpoint, 'response_time': metric.response_time},
                alert_key_prefix + '_response_critical'
            )
        elif metric.response_time > self.thresholds['response_time_warning']:
            self._create_alert(
                'response_time_warning', 
                f"High response time: {metric.response_time:.0f}ms on {metric.endpoint}",
                {'endpoint': metric.endpoint, 'response_time': metric.response_time},
                alert_key_prefix + '_response_warning'
            )
        
        # Error rate alerts  
        if stats.total_requests >= 10:  # Only alert after sufficient requests
            error_rate = ((stats.failed_requests / stats.total_requests) * 100)
            
            if error_rate > self.thresholds['error_rate_critical']:
                self._create_alert(
                    'error_rate_critical',
                    f"Critical error rate: {error_rate:.1f}% on {metric.endpoint}",
                    {'endpoint': metric.endpoint, 'error_rate': error_rate},
                    alert_key_prefix + '_error_critical'
                )
            elif error_rate > self.thresholds['error_rate_warning']:
                self._create_alert(
                    'error_rate_warning',
                    f"High error rate: {error_rate:.1f}% on {metric.endpoint}",
                    {'endpoint': metric.endpoint, 'error_rate': error_rate},
                    alert_key_prefix + '_error_warning'
                )
        
        # Throughput alerts
        current_throughput = self.real_time_metrics.get('current_throughput', 0)
        if current_throughput < self.thresholds['throughput_minimum']:
            self._create_alert(
                'throughput_low',
                f"Low throughput: {current_throughput:.2f} req/s",
                {'throughput': current_throughput},
                'throughput_low'
            )
    
    def _create_alert(self, alert_type: str, message: str, data: Dict[str, Any], cooldown_key: str):
        """Create an alert with cooldown logic"""
        
        current_time = time.time()
        
        # Check cooldown (don't spam alerts)
        if cooldown_key in self.alert_cooldown:
            if current_time - self.alert_cooldown[cooldown_key] < 60:  # 60 second cooldown
                return
        
        alert = {
            'timestamp': current_time,
            'type': alert_type,
            'message': message,
            'data': data,
            'severity': self._get_alert_severity(alert_type)
        }
        
        self.alerts.append(alert)
        self.alert_cooldown[cooldown_key] = current_time
        
        # Log alert
        logging.warning(f"PERFORMANCE ALERT: {message}")
        
        # Keep alerts list manageable
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
    
    def _get_alert_severity(self, alert_type: str) -> str:
        """Get severity level for alert type"""
        if 'critical' in alert_type:
            return 'CRITICAL'
        elif 'warning' in alert_type:
            return 'WARNING'
        else:
            return 'INFO'
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        
        total_requests = len(self.metrics_buffer)
        if total_requests == 0:
            return {'status': 'no_data'}
        
        successful_requests = sum(1 for m in self.metrics_buffer if m.success)
        failed_requests = total_requests - successful_requests
        
        all_response_times = [m.response_time for m in self.metrics_buffer]
        
        summary = {
            'test_duration': time.time() - self.start_time,
            'total_requests': total_requests,
            'successful_requests': successful_requests,
            'failed_requests': failed_requests,
            'success_rate': (successful_requests / total_requests) * 100,
            
            'response_times': {
                'average': statistics.mean(all_response_times),
                'median': statistics.median(all_response_times),
                'min': min(all_response_times),
                'max': max(all_response_times),
                'std_dev': statistics.stdev(all_response_times) if len(all_response_times) > 1 else 0,
                'percentiles': self._calculate_percentiles(all_response_times)
            },
            
            'throughput': {
                'average_rps': total_requests / (time.time() - self.start_time),
                'current_rps': self.real_time_metrics.get('current_throughput', 0)
            },
            
            'error_analysis': self._get_error_analysis(),
            'endpoint_breakdown': self._get_endpoint_breakdown(),
            'performance_score': self._calculate_performance_score(),
            'alerts_summary': self._get_alerts_summary(),
            'real_time_metrics': self.real_time_metrics
        }
        
        return summary
    
    def _calculate_percentiles(self, response_times: List[float]) -> Dict[str, float]:
        """Calculate response time percentiles"""
        if not response_times:
            return {}
        
        sorted_times = sorted(response_times)
        percentiles = {}
        
        for p in [50, 75, 90, 95, 99, 99.9]:
            index = (p / 100) * (len(sorted_times) - 1)
            if index.is_integer():
                percentiles[f'p{p}'] = sorted_times[int(index)]
            else:
                lower = sorted_times[int(index)]
                upper = sorted_times[int(index) + 1]
                percentiles[f'p{p}'] = lower + (upper - lower) * (index - int(index))
        
        return percentiles
    
    def _get_error_analysis(self) -> Dict[str, Any]:
        """Analyze error patterns"""
        
        error_metrics = [m for m in self.metrics_buffer if not m.success]
        
        if not error_metrics:
            return {'total_errors': 0}
        
        status_code_counts = defaultdict(int)
        endpoint_error_counts = defaultdict(int)
        error_messages = defaultdict(int)
        
        for metric in error_metrics:
            status_code_counts[metric.status_code] += 1
            endpoint_error_counts[metric.endpoint] += 1
            if metric.error_message:
                error_messages[metric.error_message] += 1
        
        return {
            'total_errors': len(error_metrics),
            'error_rate': (len(error_metrics) / len(self.metrics_buffer)) * 100,
            'status_code_breakdown': dict(status_code_counts),
            'endpoint_error_breakdown': dict(endpoint_error_counts),
            'common_error_messages': dict(sorted(error_messages.items(), key=lambda x: x[1], reverse=True)[:10])
        }
    
    def _get_endpoint_breakdown(self) -> Dict[str, Dict[str, Any]]:
        """Get detailed breakdown by endpoint"""
        
        breakdown = {}
        for endpoint, stats in self.endpoint_stats.items():
            breakdown[endpoint] = {
                'total_requests': stats.total_requests,
                'success_rate': stats.success_rate,
                'average_response_time': stats.average_response_time,
                'percentiles': stats.percentiles,
                'error_breakdown': dict(stats.error_counts)
            }
        
        return breakdown
    
    def _calculate_performance_score(self) -> Dict[str, Any]:
        """Calculate overall performance score (0-100)"""
        
        if not self.metrics_buffer:
            return {'score': 0, 'rating': 'NO_DATA'}
        
        # Scoring factors
        success_rate = (sum(1 for m in self.metrics_buffer if m.success) / len(self.metrics_buffer)) * 100
        avg_response_time = statistics.mean([m.response_time for m in self.metrics_buffer])
        current_throughput = self.real_time_metrics.get('current_throughput', 0)
        
        # Calculate component scores (0-100 each)
        success_score = min(100, success_rate)
        
        # Response time score (logarithmic scale)
        if avg_response_time <= 100:
            response_score = 100
        elif avg_response_time <= 500:
            response_score = 90 - (avg_response_time - 100) / 40
        elif avg_response_time <= 1000:
            response_score = 80 - (avg_response_time - 500) / 25
        elif avg_response_time <= 3000:
            response_score = 60 - (avg_response_time - 1000) / 50
        else:
            response_score = max(0, 20 - (avg_response_time - 3000) / 100)
        
        # Throughput score
        if current_throughput >= 10:
            throughput_score = 100
        elif current_throughput >= 5:
            throughput_score = 80 + (current_throughput - 5) * 4
        elif current_throughput >= 1:
            throughput_score = 60 + (current_throughput - 1) * 5
        else:
            throughput_score = max(0, current_throughput * 60)
        
        # Weighted overall score
        overall_score = (success_score * 0.4 + response_score * 0.4 + throughput_score * 0.2)
        
        # Rating
        if overall_score >= 90:
            rating = 'EXCELLENT'
        elif overall_score >= 80:
            rating = 'GOOD'
        elif overall_score >= 70:
            rating = 'FAIR'
        elif overall_score >= 60:
            rating = 'POOR'
        else:
            rating = 'CRITICAL'
        
        return {
            'score': round(overall_score, 1),
            'rating': rating,
            'components': {
                'success_rate': round(success_score, 1),
                'response_time': round(response_score, 1),
                'throughput': round(throughput_score, 1)
            }
        }
    
    def _get_alerts_summary(self) -> Dict[str, Any]:
        """Get summary of alerts"""
        
        if not self.alerts:
            return {'total_alerts': 0}
        
        severity_counts = defaultdict(int)
        type_counts = defaultdict(int)
        recent_alerts = []
        
        current_time = time.time()
        
        for alert in self.alerts:
            severity_counts[alert['severity']] += 1
            type_counts[alert['type']] += 1
            
            # Recent alerts (last 5 minutes)
            if current_time - alert['timestamp'] <= 300:
                recent_alerts.append(alert)
        
        return {
            'total_alerts': len(self.alerts),
            'severity_breakdown': dict(severity_counts),
            'type_breakdown': dict(type_counts),
            'recent_alerts': recent_alerts[-10:],  # Last 10 recent alerts
            'active_alerts': len(recent_alerts)
        }
    
    async def export_metrics(self, file_path: Path, format_type: str = 'json'):
        """Export metrics to file"""
        
        summary = self.get_performance_summary()
        
        if format_type.lower() == 'json':
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(summary, indent=2, default=str))
        
        elif format_type.lower() == 'csv':
            # Export individual metrics to CSV
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write('timestamp,endpoint,method,response_time,status_code,success,error_message\n')
                
                for metric in self.metrics_buffer:
                    await f.write(f'{metric.timestamp},{metric.endpoint},{metric.method},'
                                f'{metric.response_time},{metric.status_code},{metric.success},'
                                f'"{metric.error_message or ""}"\n')
        
        elif format_type.lower() == 'txt':
            # Human-readable text report
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write("=" * 80 + "\n")
                await f.write("ENHANCED PERFORMANCE METRICS REPORT\n")
                await f.write("=" * 80 + "\n\n")
                
                await f.write(f"Test Duration: {summary['test_duration']:.1f} seconds\n")
                await f.write(f"Total Requests: {summary['total_requests']:,}\n")
                await f.write(f"Success Rate: {summary['success_rate']:.2f}%\n")
                await f.write(f"Average Response Time: {summary['response_times']['average']:.2f}ms\n")
                await f.write(f"Throughput: {summary['throughput']['average_rps']:.2f} req/s\n")
                await f.write(f"Performance Score: {summary['performance_score']['score']}/100 ({summary['performance_score']['rating']})\n\n")
                
                # Response time percentiles
                await f.write("RESPONSE TIME PERCENTILES:\n")
                await f.write("-" * 40 + "\n")
                for percentile, value in summary['response_times']['percentiles'].items():
                    await f.write(f"{percentile}: {value:.2f}ms\n")
                await f.write("\n")
                
                # Endpoint breakdown
                await f.write("ENDPOINT PERFORMANCE:\n")
                await f.write("-" * 40 + "\n")
                for endpoint, stats in summary['endpoint_breakdown'].items():
                    await f.write(f"{endpoint}:\n")
                    await f.write(f"  Requests: {stats['total_requests']}\n")
                    await f.write(f"  Success Rate: {stats['success_rate']:.1f}%\n")
                    await f.write(f"  Avg Response: {stats['average_response_time']:.2f}ms\n")
                    await f.write(f"  95th Percentile: {stats['percentiles'].get('p95', 0):.2f}ms\n\n")
                
                # Alerts
                if summary['alerts_summary']['total_alerts'] > 0:
                    await f.write("PERFORMANCE ALERTS:\n")
                    await f.write("-" * 40 + "\n")
                    await f.write(f"Total Alerts: {summary['alerts_summary']['total_alerts']}\n")
                    
                    for severity, count in summary['alerts_summary']['severity_breakdown'].items():
                        await f.write(f"{severity}: {count}\n")
                    await f.write("\n")
                    
                    # Recent alerts
                    if summary['alerts_summary']['recent_alerts']:
                        await f.write("RECENT ALERTS:\n")
                        for alert in summary['alerts_summary']['recent_alerts'][-5:]:
                            timestamp = datetime.fromtimestamp(alert['timestamp']).strftime('%H:%M:%S')
                            await f.write(f"[{timestamp}] {alert['severity']}: {alert['message']}\n")
                
                await f.write(f"\nReport generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")


class RealTimeMonitor:
    """Real-time performance monitor with live updates"""
    
    def __init__(self, metrics_collector: PerformanceMetricsCollector):
        self.metrics_collector = metrics_collector
        self.monitoring = False
        self.update_interval = 5.0  # seconds
        self.monitor_task = None
        
    async def start_monitoring(self, output_file: Optional[Path] = None):
        """Start real-time monitoring"""
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(output_file))
        
    async def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
    
    async def _monitor_loop(self, output_file: Optional[Path]):
        """Main monitoring loop"""
        
        while self.monitoring:
            try:
                # Get current metrics
                summary = self.metrics_collector.get_performance_summary()
                
                # Print to console
                self._print_real_time_status(summary)
                
                # Write to file if specified
                if output_file:
                    await self._write_monitoring_data(output_file, summary)
                
                # Wait for next update
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logging.error(f"Monitor loop error: {e}")
                await asyncio.sleep(1)
    
    def _print_real_time_status(self, summary: Dict[str, Any]):
        """Print real-time status to console"""
        
        if summary.get('status') == 'no_data':
            print("â³ Waiting for metrics data...")
            return
        
        # Clear screen (basic version)
        print("\n" * 2)
        print("=" * 60)
        print("ðŸŽ¯ REAL-TIME PERFORMANCE MONITOR")
        print("=" * 60)
        
        # Key metrics
        print(f"ðŸ“Š Requests: {summary['total_requests']:,} | "
              f"Success: {summary['success_rate']:.1f}% | "
              f"RPS: {summary['real_time_metrics'].get('current_throughput', 0):.1f}")
        
        print(f"âš¡ Response: {summary['response_times']['average']:.0f}ms avg | "
              f"{summary['response_times']['percentiles'].get('p95', 0):.0f}ms p95 | "
              f"{summary['response_times']['max']:.0f}ms max")
        
        # Performance score
        score = summary['performance_score']['score']
        rating = summary['performance_score']['rating']
        print(f"ðŸ† Performance Score: {score}/100 ({rating})")
        
        # Active alerts
        active_alerts = summary['alerts_summary'].get('active_alerts', 0)
        if active_alerts > 0:
            print(f"âš ï¸  Active Alerts: {active_alerts}")
        
        print("=" * 60)
        print(f"â±ï¸  Uptime: {summary.get('test_duration', 0):.0f}s | "
              f"Last Update: {datetime.now().strftime('%H:%M:%S')}")
        print()
    
    async def _write_monitoring_data(self, output_file: Path, summary: Dict[str, Any]):
        """Write monitoring data to file"""
        
        timestamp = datetime.now().isoformat()
        monitoring_data = {
            'timestamp': timestamp,
            'summary': summary
        }
        
        # Append to monitoring log file
        async with aiofiles.open(output_file, 'a', encoding='utf-8') as f:
            await f.write(json.dumps(monitoring_data) + '\n')


# Integration with existing Test.Charge framework
class TestChargeMetricsIntegration:
    """Integration layer for Test.Charge metrics"""
    
    def __init__(self):
        self.metrics_collector = PerformanceMetricsCollector()
        self.real_time_monitor = RealTimeMonitor(self.metrics_collector)
        
    async def start_monitoring(self):
        """Start enhanced monitoring"""
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        monitoring_file = logs_dir / f"real_time_metrics_{int(time.time())}.jsonl"
        await self.real_time_monitor.start_monitoring(monitoring_file)
        
    async def stop_monitoring_and_report(self):
        """Stop monitoring and generate final report"""
        await self.real_time_monitor.stop_monitoring()
        
        # Generate final reports
        reports_dir = Path("reports")
        reports_dir.mkdir(exist_ok=True)
        
        timestamp = int(time.time())
        
        # Export in multiple formats
        await self.metrics_collector.export_metrics(
            reports_dir / f"enhanced_metrics_{timestamp}.json", 'json'
        )
        await self.metrics_collector.export_metrics(
            reports_dir / f"enhanced_metrics_{timestamp}.csv", 'csv'
        )
        await self.metrics_collector.export_metrics(
            reports_dir / f"enhanced_metrics_{timestamp}.txt", 'txt'
        )
        
        print(f"ðŸ“Š Enhanced metrics reports saved to reports/ directory")
        
        return self.metrics_collector.get_performance_summary()
    
    def record_request(self, endpoint: str, method: str, response_time: float, 
                      status_code: int, response_length: int = 0, 
                      error_message: Optional[str] = None, 
                      thread_id: Optional[int] = None, user_id: Optional[str] = None):
        """Record a request metric"""
        
        metric = RequestMetric(
            timestamp=time.time(),
            endpoint=endpoint,
            method=method,
            response_time=response_time,
            status_code=status_code,
            response_length=response_length,
            success=200 <= status_code < 400,
            error_message=error_message,
            thread_id=thread_id,
            user_id=user_id
        )
        
        self.metrics_collector.add_metric(metric)
    
    def get_current_performance(self) -> Dict[str, Any]:
        """Get current performance summary"""
        return self.metrics_collector.get_performance_summary()


# Example usage and integration
if __name__ == "__main__":
    async def demo_enhanced_metrics():
        """Demonstrate enhanced metrics system"""
        
        integration = TestChargeMetricsIntegration()
        
        # Start monitoring
        await integration.start_monitoring()
        
        # Simulate some requests
        import random
        
        endpoints = ['/andoc/session/new', '/andoc/chat/send', '/health']
        methods = ['GET', 'POST']
        
        for i in range(100):
            endpoint = random.choice(endpoints)
            method = random.choice(methods)
            response_time = random.uniform(100, 2000)
            status_code = random.choices([200, 201, 400, 500], weights=[0.8, 0.1, 0.05, 0.05])[0]
            
            integration.record_request(
                endpoint=endpoint,
                method=method,
                response_time=response_time,
                status_code=status_code,
                thread_id=i % 5
            )
            
            await asyncio.sleep(0.1)
        
        # Stop and generate report
        final_summary = await integration.stop_monitoring_and_report()
        
        print("Demo completed! Check reports/ directory for enhanced metrics.")
        return final_summary
    
    # Run the demo
    asyncio.run(demo_enhanced_metrics())

class AzureOpenAIMonitor:
    """Moniteur spécialisé pour les appels Azure OpenAI"""

    def __init__(self, log_file: Optional[str] = None):
        # self.setup_logging(log_file)
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "rate_limited_requests": 0,
            "tokens_used": 0,
            "models_used": {},
            "error_types": {},
        }

    def setup_logging(self, log_file: Optional[str] = None):
        """Configure le logging spécialisé pour Azure OpenAI"""
        if not log_file:
            logs_dir = Path("logs")
            logs_dir.mkdir(exist_ok=True)
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            log_file = logs_dir / f"azure_openai_{timestamp}.log"

        self.logger = logging.getLogger("azure_openai_monitor")
        self.logger.setLevel(logging.INFO)

        # éviter les doublons de handlers
        if not self.logger.handlers:
            handler = logging.FileHandler(log_file, encoding="utf-8")
            formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def is_openai_request(self, endpoint: str) -> bool:
        """Détecte si c'est un appel vers Azure OpenAI"""
        openai_patterns = [
            r"/openai/",
            r"/chat/completions",
            r"/completions",
            r"/embeddings",
            r"\.openai\.azure\.com",
            r"/api/ai/",  # Pattern personnalisé pour votre API
        ]

        return any(
            re.search(pattern, endpoint, re.IGNORECASE) for pattern in openai_patterns
        )

    async def log_request(
        self,
        endpoint: str,
        method: str,
        data: Dict[str, Any] = None,
        thread_id: int = 0,
        response_data: Dict[str, Any] = None,
        status_code: int = 200,
        duration: float = 0.0,
    ):
        """Log une requète OpenAI avec ses détails"""

        if not self.is_openai_request(endpoint):
            return

        self.stats["total_requests"] += 1

        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "thread_id": thread_id,
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "duration_ms": round(duration * 1000, 2),
            "type": "openai_request",
        }

        # Extraire des informations du payload
        if data:
            log_entry.update(self._extract_request_info(data))

        # Extraire des informations de la réponse
        if response_data:
            log_entry.update(self._extract_response_info(response_data))

        # Mettreà  jour les statistiques
        self._update_stats(log_entry, status_code)

        # Logger selon le niveau approprié
        if status_code >= 400:
            self.logger.error(json.dumps(log_entry, ensure_ascii=False))
        elif status_code == 429:  # Rate limited
            self.logger.warning(json.dumps(log_entry, ensure_ascii=False))
        else:
            self.logger.info(json.dumps(log_entry, ensure_ascii=False))

    def _extract_request_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrait des informations utiles du payload de requète"""
        info = {}

        # ModÃ¨le utilisé
        if "model" in data:
            info["model"] = data["model"]

        # Nombre de tokens estimé (pour les messages)
        if "messages" in data:
            estimated_tokens = self._estimate_tokens(data["messages"])
            info["estimated_input_tokens"] = estimated_tokens

        # Température et autres paramÃ¨tres
        for param in ["temperature", "max_tokens", "top_p", "presence_penalty"]:
            if param in data:
                info[param] = data[param]

        return info

    def _extract_response_info(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrait des informations utiles de la réponse"""
        info = {}

        # Utilisation des tokens (Azure OpenAI format)
        if "usage" in response_data:
            usage = response_data["usage"]
            info["tokens_used"] = usage.get("total_tokens", 0)
            info["prompt_tokens"] = usage.get("prompt_tokens", 0)
            info["completion_tokens"] = usage.get("completion_tokens", 0)

        # Informations de rate limiting
        if "error" in response_data:
            error = response_data["error"]
            info["error_type"] = error.get("type", "unknown")
            info["error_code"] = error.get("code", "unknown")

            # Retry-After pour les erreurs 429
            if "retry_after" in error:
                info["retry_after_seconds"] = error["retry_after"]

        return info

    def _estimate_tokens(self, messages: list) -> int:
        """Estimation approximative du nombre de tokens"""
        total_chars = 0
        for message in messages:
            if isinstance(message, dict) and "content" in message:
                total_chars += len(str(message["content"]))

        # Estimation grossiÃ¨re : ~4 caractÃ¨res par token
        return total_chars // 4

    def _update_stats(self, log_entry: Dict[str, Any], status_code: int):
        """Metà  jour les statistiques internes"""

        if status_code < 400:
            self.stats["successful_requests"] += 1
        else:
            self.stats["failed_requests"] += 1

        if status_code == 429:
            self.stats["rate_limited_requests"] += 1

        # Compter les tokens utilisés
        if "tokens_used" in log_entry:
            self.stats["tokens_used"] += log_entry["tokens_used"]

        # Compter les modÃ¨les utilisés
        if "model" in log_entry:
            model = log_entry["model"]
            self.stats["models_used"][model] = (
                self.stats["models_used"].get(model, 0) + 1
            )

        # Compter les types d'erreurs
        if "error_type" in log_entry:
            error_type = log_entry["error_type"]
            self.stats["error_types"][error_type] = (
                self.stats["error_types"].get(error_type, 0) + 1
            )

    async def generate_report(self) -> str:
        """GénÃ¨re un rapport des statistiques Azure OpenAI"""

        report = [
            "\n=== RAPPORT AZURE OPENAI ===",
            f"Total des requètes: {self.stats['total_requests']}",
            f"Requètes réussies: {self.stats['successful_requests']}",
            f"Requètes échouées: {self.stats['failed_requests']}",
            f"Requètes rate-limited: {self.stats['rate_limited_requests']}",
            f"Tokens total utilisés: {self.stats['tokens_used']:,}",
        ]

        # Taux de succÃ¨s
        if self.stats["total_requests"] > 0:
            success_rate = (
                self.stats["successful_requests"] / self.stats["total_requests"]
            ) * 100
            report.append(f"Taux de succÃ¨s: {success_rate:.1f}%")

        # ModÃ¨les utilisés
        if self.stats["models_used"]:
            report.append("\nModÃ¨les utilisés:")
            for model, count in self.stats["models_used"].items():
                report.append(f"  - {model}: {count} requètes")

        # Types d'erreurs
        if self.stats["error_types"]:
            report.append("\nTypes d'erreurs:")
            for error_type, count in self.stats["error_types"].items():
                report.append(f"  - {error_type}: {count} occurrences")

        return "\n".join(report)


class AsyncioMonitor:
    """Monitoring class for asyncio-based load testing"""
    
    def __init__(self, test_type: str = "standard"):
        self.metrics_collector = PerformanceMetricsCollector()
        self.test_type = test_type
        self.start_time = None
        self.end_time = None
        self.active_workers = set()
        self.worker_metrics = {}
        self.monitoring_task = None
        self.test_running = False
        
    def start_worker(self, worker_id: int):
        """Register a worker starting"""
        self.active_workers.add(worker_id)
        self.worker_metrics[worker_id] = {
            'start_time': time.time(),
            'requests': 0,
            'errors': 0
        }
        
    def end_worker(self, worker_id: int):
        """Register a worker ending"""
        if worker_id in self.active_workers:
            self.active_workers.discard(worker_id)
            if worker_id in self.worker_metrics:
                self.worker_metrics[worker_id]['end_time'] = time.time()
    
    def record_request(self, metric: RequestMetric):
        """Record a request metric"""
        self.metrics_collector.add_metric(metric)
        
        # Update worker metrics if thread_id is provided
        if metric.thread_id is not None and metric.thread_id in self.worker_metrics:
            self.worker_metrics[metric.thread_id]['requests'] += 1
            if not metric.success:
                self.worker_metrics[metric.thread_id]['errors'] += 1
    
    def start_test(self):
        """Mark the start of a test"""
        self.start_time = time.time()
        self.test_running = True
        
    async def live_monitoring_task(self, interval: int = 5):
        """Run live monitoring in the background"""
        while self.test_running:
            try:
                stats = self.get_current_stats()
                self._print_live_stats(stats)
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.error(f"Error in live monitoring: {e}")
                await asyncio.sleep(interval)
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current test statistics"""
        summary = self.metrics_collector.get_performance_summary()
        
        # Add worker-specific stats
        summary['active_workers'] = len(self.active_workers)
        summary['total_workers'] = len(self.worker_metrics)
        
        return summary
    
    def end_test(self):
        """Mark the end of a test"""
        self.end_time = time.time()
        self.test_running = False
        
        # Cancel monitoring task if running
        if self.monitoring_task:
            self.monitoring_task.cancel()
    
    def print_summary(self, test_type: str = "asyncio", duration: float = None, 
                     num_users: int = None, context_file: str = None, base_url: str = None):
        """Print a summary of the test results"""
        summary = self.metrics_collector.get_performance_summary()
        
        print("\n" + "=" * 60)
        print(f"TEST SUMMARY - {test_type.upper()}")
        print("=" * 60)
        
        if context_file:
            print(f"Context: {context_file}")
        if base_url:
            print(f"Target: {base_url}")
        if duration:
            print(f"Duration: {duration:.1f}s")
        if num_users:
            print(f"Users: {num_users}")
            
        print(f"Total Requests: {summary['total_requests']:,}")
        print(f"Success Rate: {summary['success_rate']:.2f}%")
        
        if summary['response_times']['average'] > 0:
            print(f"Avg Response Time: {summary['response_times']['average']:.0f}ms")
            print(f"P95 Response Time: {summary['response_times']['percentiles'].get('p95', 0):.0f}ms")
            print(f"Max Response Time: {summary['response_times']['max']:.0f}ms")
        
        # Endpoint breakdown
        if summary.get('endpoint_breakdown'):
            print("\nEndpoint Performance:")
            for endpoint, stats in summary['endpoint_breakdown'].items():
                print(f"  {endpoint}:")
                print(f"    Requests: {stats['total_requests']:,}")
                print(f"    Success: {stats['success_rate']:.1f}%")
                print(f"    Avg Time: {stats['average_response_time']:.0f}ms")
        
        print("=" * 60)
    
    def export_csv_report(self, filename: str = None) -> str:
        """Export metrics to CSV format"""
        import csv
        from pathlib import Path
        
        # Create reports directory if it doesn't exist
        reports_dir = Path("reports")
        reports_dir.mkdir(exist_ok=True)
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = reports_dir / f"asyncio_report_{timestamp}.csv"
        else:
            filename = reports_dir / filename
        
        # Write metrics to CSV
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['timestamp', 'endpoint', 'method', 'response_time', 
                         'status_code', 'success', 'thread_id']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for metric in self.metrics_collector.metrics_buffer:
                writer.writerow({
                    'timestamp': metric.timestamp,
                    'endpoint': metric.endpoint,
                    'method': metric.method,
                    'response_time': metric.response_time,
                    'status_code': metric.status_code,
                    'success': metric.success,
                    'thread_id': metric.thread_id
                })
        
        print(f"CSV report exported to: {filename}")
        return str(filename)
    
    def export_json_report(self, filename: str = None) -> str:
        """Export test summary to JSON format"""
        from pathlib import Path
        
        # Create reports directory if it doesn't exist
        reports_dir = Path("reports")
        reports_dir.mkdir(exist_ok=True)
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = reports_dir / f"asyncio_summary_{timestamp}.json"
        else:
            filename = reports_dir / filename
        
        # Get summary data
        summary = self.metrics_collector.get_performance_summary()
        
        # Add test metadata
        summary['test_metadata'] = {
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': (self.end_time - self.start_time) if self.end_time and self.start_time else None,
            'total_workers': len(self.worker_metrics),
            'worker_details': self.worker_metrics
        }
        
        # Write to JSON file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print(f"JSON report exported to: {filename}")
        return str(filename)
    
    def _print_live_stats(self, stats: Dict[str, Any]):
        """Print live statistics during test execution"""
        if stats.get('status') == 'no_data':
            return
            
        # Simple live stats display
        elapsed = time.time() - self.start_time if self.start_time else 0
        print(f"\r[{elapsed:.0f}s] Requests: {stats['total_requests']:,} | "
              f"Success: {stats['success_rate']:.1f}% | "
              f"RPS: {stats['real_time_metrics'].get('current_throughput', 0):.1f} | "
              f"Avg: {stats['response_times']['average']:.0f}ms", end="", flush=True)
#!/usr/bin/env python
"""Test script to verify Locust configuration loading"""

import os
import sys
import json
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set environment for testing
os.environ["CONFIG_CONTEXT"] = "localapi_simple"
os.environ["USER_TYPES"] = "config"

def test_config_loading():
    """Test that configuration can be loaded properly"""
    
    config_path = project_root / "configs" / "localapi_simple.json"
    
    if not config_path.exists():
        print(f"ERROR: Config file not found: {config_path}")
        return False
        
    with open(config_path, "r", encoding="utf-8") as f:
        config = json.load(f)
    
    print("Configuration loaded successfully!")
    print(f"Base URL: {config.get('base_url')}")
    print(f"Number of endpoints: {len(config.get('endpoints', []))}")
    
    # Check endpoints
    endpoints = config.get("endpoints", [])
    for i, endpoint in enumerate(endpoints[:3]):  # Show first 3
        print(f"  Endpoint {i+1}: {endpoint.get('method', 'GET')} {endpoint.get('url')}")
        if endpoint.get("headers"):
            for key, value in endpoint.get("headers", {}).items():
                if "token" in value.lower() or "authorization" in key.lower():
                    print(f"    - Has auth header: {key}")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("Testing Locust Configuration Loading")
    print("=" * 60)
    
    success = test_config_loading()
    
    print("\n" + "=" * 60)
    if success:
        print("[OK] Configuration test PASSED")
        print("\nSummary of fixes applied:")
        print("1. Fixed import error - changed from relative to absolute imports")
        print("2. Created ConfigEndpointsUser to use actual config endpoints")
        print("3. Disabled hardcoded /andoc endpoint users")
        print("4. Fixed Authorization header (was X-CGPT-AUTHORIZATION)")
        print("5. Added header formatting with token substitution")
        print("\nWhen Locust is installed, it will use the configured endpoints")
        print("from localapi_simple.json instead of hardcoded /andoc endpoints.")
    else:
        print("[FAILED] Configuration test FAILED")
    print("=" * 60)
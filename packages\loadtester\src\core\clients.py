from typing import Dict, Any, Optional, List, Tuple, Union
import httpx
import time
import warnings
import mimetypes
import os
import aiofiles
import traceback

from .timer import Timer, TimingResult
from .monitoring import AzureOpenAIMonitor

class HttpClient:
    """Client HTTP qui gÃ¨re les appels avec authentification JWT."""

    def __init__(
        self, 
        base_url: str, 
        jwt_token: str, 
        timer: Timer,
        verify_ssl: bool = True,
        config: Dict[str, Any] = None,
    ):
        self.base_url = base_url
        self.jwt_token = jwt_token
        self.timer = timer
        self.results = {}  # Pour stocker les résultats des appels précédents
        self.results_by_id = {}  # Pour stocker les résultats par ID personnalisé
        self.verify_ssl = verify_ssl
        self.openai_monitor = AzureOpenAIMonitor()
        
        # Debug and enhanced error handling options
        config = config or {}
        self.debug_mode = config.get('debug_mode', False)
        self.verbose_errors = config.get('verbose_errors', False)
        self.log_request_headers = config.get('enhanced_logging', {}).get('log_request_headers', False)
        self.log_response_headers = config.get('enhanced_logging', {}).get('log_response_headers', False)
        self.log_full_response_on_error = config.get('enhanced_logging', {}).get('log_full_response_on_error', False)

        # Configuration des timeouts
        config = config or {}
        self.timeouts = httpx.Timeout(
            connect=config.get("timeout_connect", 10.0),
            read=config.get("timeout_read", 30.0),
            write=config.get("timeout_write", 30.0),
            pool=config.get("timeout_pool", 10.0)
        )

        if not verify_ssl:
            # Supprimer les avertissements pour les requètes non sécurisées
            warnings.filterwarnings("ignore", message="Unverified HTTPS request")

    def _setup_timeouts(self, config: Dict[str, Any]) -> httpx.Timeout:
        """Configure les timeouts basés sur la configuration"""
        return httpx.Timeout(
            connect=config.get("timeout_connect", 10.0),
            read=config.get("timeout_read", 30.0),
            write=config.get("timeout_write", 30.0),
            pool=config.get("timeout_pool", 10.0),
        )

    async def make_request(
        self,
        endpoint: str,
        method: str = "GET",
        headers: Dict[str, str] = None,
        data: Dict[str, Any] = None,
        extract_key: Optional[str] = None,
        extract_id: Optional[str] = None,
        extract: Optional[List[Dict[str, str]]] = None,
        thread_id: int = 0,
        endpoint_id: Optional[str] = None,
        file_path: Optional[str] = None,
        form_name: str = "file",
        current_step: int = 0,
        total_steps: int = 0,
    ) -> Tuple[Dict[str, Any], bool]:
        """
        Fait une requète HTTP et chronomÃ¨tre le temps de réponse.
        Retourne (données_réponse, continuer_séquence).
        """
        url = f"{self.base_url}{endpoint}"
        start_time = time.time()
        response_data = {"error": "Une erreur inconnue s'est produite"}
        status_code = 0
        files = None
        error_url = url
        error_exc_type = None  # Initialize to None by default

        # Afficher la progression
        if total_steps > 0:
            print(
                f"Thread {thread_id}: étape {current_step}/{total_steps} - {endpoint_id or endpoint}"
            )

        try:
            # Préparer les en-tètes avec le jeton JWT
            request_headers = self._prepare_headers(headers)

            async with httpx.AsyncClient(verify=self.verify_ssl) as client:
                # Exécuter la requète HTTP
                response, status_code = await self._execute_request(
                    client, url, method, request_headers, data, file_path, form_name
                )

                # Vérifier s'il y a des erreurs d'authentification
                if self._is_auth_error(status_code):
                    error_msg = f"Authentification refusée (HTTP {status_code})"
                    
                    # Enhanced debugging for auth errors
                    if self.debug_mode or self.verbose_errors:
                        print(f"ðŸ” Authentication Error Details:")
                        print(f"   Status Code: {status_code}")
                        print(f"   URL: {url}")
                        print(f"   JWT Token (first 30 chars): {self.jwt_token[:30]}...")
                        
                        if self.log_response_headers:
                            print(f"   Response Headers: {dict(response.headers)}")
                        
                        if self.log_full_response_on_error:
                            try:
                                print(f"   Response Body: {response.text[:500]}")
                            except:
                                pass
                    
                    # For load testing, continue the sequence even with auth errors
                    # to gather statistics on all endpoints
                    return {"error": error_msg}, True

                # Traiter la réponse et extraire les données
                response_data = self._process_response(
                    response,
                    status_code,
                    endpoint,
                    extract,
                    extract_key,
                    extract_id,
                    endpoint_id,
                )

                # Monitoring spécialisé pour Azure OpenAI
                await self.openai_monitor.log_request(
                    endpoint=endpoint,
                    method=method,
                    data=data,
                    thread_id=thread_id,
                    response_data=response_data,
                    status_code=status_code,
                    duration=time.time() - start_time,
                )
        except Exception as e:
            # Gérer les différents types d'erreurs
            response_data, status_code, exc_type = self._handle_request_exception(
                e, endpoint, return_exc_type=True
            )
            error_exc_type = exc_type
        finally:
            # Fermer les ressources de fichier si nécessaires
            self._close_file_resources(files)

            # Enregistrer les métriques de temps d'exécution
            self._record_timing_result(
                endpoint, thread_id, start_time, time.time(), status_code, endpoint_id
            )

            # Log des erreurs HTTP (code >= 400 ou < 0)
            if status_code >= 400 or status_code < 0:
                response_content = None
                if "response" in locals() and response is not None:
                    try:
                        response_content = response.text
                    except Exception:
                        response_content = str(response)
                else:
                    response_content = str(response_data)
                # Ajout : passer le type d'exception au log d'erreur si status_code < 0
                await self.timer.log_error_response(
                    endpoint,
                    thread_id,
                    status_code,
                    response_content,
                    endpoint_id,
                    url=error_url,
                    exception_message=error_exc_type,
                )
        return response_data, True  # Par défaut, continuer la séquence

    def _prepare_headers(
        self, custom_headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, str]:
        """Prépare les en-tètes de la requète avec le jeton JWT."""
        headers = {}
        
        # Start with custom headers if provided (they may contain properly formatted Authorization)
        if custom_headers:
            headers.update(custom_headers)
        
        # Only add JWT token from self if no Authorization header is already present
        # and if we have a non-empty token
        if "Authorization" not in headers and self.jwt_token and self.jwt_token.strip():
            headers["Authorization"] = f"Bearer {self.jwt_token}"
        
        # Debug logging if enabled
        if hasattr(self, 'debug_mode') and self.debug_mode:
            auth_header = headers.get("Authorization", "None")
            if auth_header != "None":
                # Show first 30 chars of the auth header for debugging
                display_auth = auth_header[:40] + "..." if len(auth_header) > 40 else auth_header
                print(f"[DEBUG] Auth header: {display_auth}")
        
        return headers

    async def _execute_request(
        self,
        client: httpx.AsyncClient,
        url: str,
        method: str,
        headers: Dict[str, str],
        data: Optional[Dict[str, Any]],
        file_path: Optional[str],
        form_name: str,
    ) -> Tuple[httpx.Response, int]:
        """Exécute la requète HTTP selon la méthode spécifiée."""
        response = None

        if file_path:
            # Timeout plus long pour les uploads
            upload_timeout = httpx.Timeout(
                connect=self.timeouts.connect,
                read=self.timeouts.read * 2,  # Double pour les uploads
                write=self.timeouts.write * 2,
                pool=self.timeouts.pool,
            )

            response = await self._upload_file(
                client, url, headers, file_path, form_name, upload_timeout
            )
        elif method.upper() == "GET":
            response = await client.get(url, headers=headers, params=data, timeout=self.timeouts)
        elif method.upper() == "POST":
            response = await client.post(url, headers=headers, json=data, timeout=self.timeouts)
        elif method.upper() == "PUT":
            response = await client.put(url, headers=headers, json=data, timeout=self.timeouts)
        elif method.upper() == "DELETE":
            response = await client.delete(url, headers=headers, json=data, timeout=self.timeouts)
        else:
            raise ValueError(f"Méthode HTTP non supportée: {method}")

        return response, response.status_code

    async def _upload_file(
        self,
        client: httpx.AsyncClient,
        url: str,
        headers: Dict[str, str],
        file_path: str,
        form_name: str,
        timeout: httpx.Timeout = None,
    ) -> httpx.Response:
        """Téléverse un fichier vers le serveur."""
        try:
            # Vérifie si le fichier existe
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Le fichier '{file_path}' n'existe pas")

            # Détermine le type MIME du fichier
            file_type, _ = mimetypes.guess_type(file_path)
            if not file_type:
                file_type = "application/octet-stream"

            # Lire le contenu du fichier de faÃ§on asynchrone
            async with aiofiles.open(file_path, "rb") as f:
                file_content = await f.read()

            # Créer un form multipart avec le contenu du fichier
            files = {form_name: (os.path.basename(file_path), file_content, file_type)}

            print(f"Téléversement du fichier: {file_path} vers {url}")
            effective_timeout = timeout or self.timeouts
            return await client.post(url, headers=headers, files=files, timeout=effective_timeout)
        except Exception as e:
            print(f"Erreur lors du téléversement du fichier {file_path}: {str(e)}")
            raise e

    def _is_auth_error(self, status_code: int) -> bool:
        """Vérifie si le code de statut indique une erreur d'authentification."""
        return status_code in (401, 403)

    def _process_response(
        self,
        response: httpx.Response,
        status_code: int,
        endpoint: str,
        extract: Optional[List[Dict[str, str]]],
        extract_key: Optional[str],
        extract_id: Optional[str],
        endpoint_id: Optional[str],
    ) -> Dict[str, Any]:
        """Traite la réponse HTTP et extrait les données si nécessaire."""
        # Gestion des erreurs serveur
        if 500 <= status_code < 600:
            return self._handle_server_error(response, status_code)

        # Traitement de la réponse
        try:
            response_data = response.json()
        except Exception as e:
            return {"text": response.text, "error_parsing": str(e)}

        # Extraction des valeurs si nécessaire
        if status_code < 400:
            self._extract_values(
                response_data, endpoint, extract, extract_key, extract_id, endpoint_id
            )

        return response_data

    def _handle_server_error(
        self, response: httpx.Response, status_code: int
    ) -> Dict[str, Any]:
        """GÃ¨re les erreurs serveur (5xx)."""
        error_message = "Erreur interne du serveur"
        try:
            # Essayer de récupérer le message d'erreur au format JSON
            error_data = response.json()
            if isinstance(error_data, dict):
                if "message" in error_data:
                    error_message = error_data["message"]
                elif "error" in error_data:
                    error_message = error_data["error"]
                elif "detail" in error_data:
                    error_message = error_data["detail"]
        except Exception:
            # Si le contenu n'est pas du JSON valide, récupérer le texte brut
            try:
                error_message = response.text[:200]  # Limiterà  200 caractÃ¨res
            except Exception:
                pass  # Garder le message par défaut si tout échoue

        print(f"Erreur serveur (HTTP {status_code}): {error_message}")
        return {"error": error_message, "status_code": status_code}

    def _extract_values(
        self,
        response_data: Dict[str, Any],
        endpoint: str,
        extract: Optional[List[Dict[str, str]]],
        extract_key: Optional[str],
        extract_id: Optional[str],
        endpoint_id: Optional[str],
    ) -> None:
        """Extrait les valeurs de la réponse selon les configurations données."""
        # Traitement du nouveau format d'extraction (tableau d'objets)
        if extract:
            self._extract_values_from_list(response_data, extract, endpoint)

        # Ancien format d'extraction (pour compatibilité)
        elif extract_key:
            self._extract_single_value(
                response_data, extract_key, endpoint, endpoint_id, extract_id
            )

    def _extract_values_from_list(
        self,
        response_data: Dict[str, Any],
        extract_list: List[Dict[str, str]],
        endpoint: str,
    ) -> None:
        """Extrait plusieurs valeursà  partir d'une liste de configurations."""
        for extraction in extract_list:
            # Support both camelCase and snake_case for compatibility
            if "key" in extraction and ("valueFrom" in extraction or "value_from" in extraction):
                key_id = extraction["key"]
                value_key = extraction.get("valueFrom") or extraction.get("value_from")

                try:
                    if value_key in response_data:
                        extracted_value = response_data[value_key]
                        self.results_by_id[key_id] = extracted_value
                        # Debug token extraction
                        if key_id == "token":
                            token_preview = str(extracted_value)[:20] + "..." if len(str(extracted_value)) > 20 else str(extracted_value)
                            print(f"[DEBUG] Token extracted from {endpoint}: {token_preview}")
                    else:
                        print(
                            f"Avertissement: Clé '{value_key}' non trouvée dans la réponse de {endpoint}"
                        )
                except Exception as e:
                    print(
                        f"Erreur lors de l'extraction de la clé '{value_key}': {str(e)}"
                    )

    def _extract_single_value(
        self,
        response_data: Dict[str, Any],
        extract_key: str,
        endpoint: str,
        endpoint_id: Optional[str],
        extract_id: Optional[str],
    ) -> None:
        """Extrait une seule valeur de la réponse."""
        try:
            if extract_key in response_data:
                # Stocke le résultat pour référence future (par URL et par ID)
                extracted_value = response_data[extract_key]
                self.results[endpoint] = extracted_value

                # Si un ID personnalisé est fourni, stocke également le résultat par ID
                if endpoint_id:
                    self.results_by_id[endpoint_id] = extracted_value
                    print(
                        f"Valeur extraite de {endpoint} (ID: {endpoint_id}): {extract_key}={extracted_value}"
                    )

                # Si un ID spécifique pour la valeur extraite est fourni, l'utiliser aussi
                if extract_id:
                    self.results_by_id[extract_id] = extracted_value
                    print(
                        f"Valeur extraite associéeà  l'ID personnalisé: {extract_id}={extracted_value}"
                    )

                if not endpoint_id and not extract_id:
                    print(
                        f"Valeur extraite de {endpoint}: {extract_key}={extracted_value}"
                    )
            else:
                print(
                    f"Avertissement: Clé '{extract_key}' non trouvée dans la réponse de {endpoint}"
                )
        except Exception as e:
            print(f"Erreur lors de l'extraction de la clé '{extract_key}': {str(e)}")

    def _handle_request_exception(
        self, exception: Exception, endpoint: str, return_exc_type: bool = False
    ) -> Union[Tuple[Dict[str, Any], int], Tuple[Dict[str, Any], int, str]]:
        """GÃ¨re les exceptions survenues pendant la requète."""
        if isinstance(exception, FileNotFoundError):
            status_code = -3
            exc_type = type(exception).__name__
            response_data = {"error": str(exception), "type": exc_type}
            print(f"Erreur de fichier: {str(exception)}")
        elif isinstance(exception, httpx.RequestError):
            status_code = -1
            exc_type = type(exception).__name__
            response_data = {
                "error": f"Erreur de requète HTTP: {str(exception)}",
                "type": exc_type,
            }
            print(f"Erreur lors de l'appelà  {endpoint}: {str(exception)}")
        else:
            status_code = -2
            exc_type = type(exception).__name__
            response_data = {"error": f"Exception: {str(exception)}", "type": exc_type}
            print(f"Exception lors de l'appelà  {endpoint}: {str(exception)}")
            traceback.print_exc()

        if return_exc_type:
            return response_data, status_code, exc_type
        return response_data, status_code

    def _close_file_resources(self, files: Optional[Dict]) -> None:
        """Ferme les ressources de fichiers ouvertes."""
        if files:
            for f in files.values():
                if hasattr(f[1], "close"):
                    f[1].close()

    def _record_timing_result(
        self,
        endpoint: str,
        thread_id: int,
        start_time: float,
        end_time: float,
        status_code: int,
        endpoint_id: Optional[str],
    ) -> None:
        """Enregistre les résultats de timing pour la requète."""
        result = TimingResult(
            endpoint=endpoint,
            thread_id=thread_id,
            start_time=start_time,
            end_time=end_time,
            status_code=status_code,
            endpoint_id=endpoint_id,
        )
        self.timer.add_result(result)
        print(result)
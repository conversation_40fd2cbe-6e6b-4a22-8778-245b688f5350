#!/usr/bin/env python
"""Test script to verify Locust authentication flow"""

import json
import re
from pathlib import Path

class MockExtractedVars:
    """Mock extracted variables for testing"""
    def __init__(self):
        self.extracted_variables = {}
    
    def extract_token(self, response_data):
        """Simulate token extraction from login response"""
        config = [{"key": "token", "valueFrom": "access_token"}]
        
        for extraction in config:
            if "key" in extraction and ("valueFrom" in extraction or "value_from" in extraction):
                key_id = extraction["key"]
                value_key = extraction.get("valueFrom") or extraction.get("value_from")
                
                if isinstance(response_data, dict) and value_key in response_data:
                    extracted_value = response_data[value_key]
                    self.extracted_variables[key_id] = extracted_value
                    print(f"[OK] Extracted {key_id} = {extracted_value[:20]}...")
    
    def format_string_with_variables(self, template):
        """Format string template with extracted variables"""
        if not isinstance(template, str) or "{" not in template:
            return template
        
        var_pattern = r"\{([^{}]+)\}"
        required_vars = re.findall(var_pattern, template)
        
        missing_vars = [
            var for var in required_vars if var not in self.extracted_variables
        ]
        
        if missing_vars:
            print(f"[INFO] Variables not yet extracted: {missing_vars}")
            if "token" in missing_vars and "Bearer" in template:
                return ""  # Return empty string to skip this header
            return template
        
        try:
            return template.format(**self.extracted_variables)
        except KeyError as e:
            print(f"[ERROR] Failed to format: {e}")
            return template

def test_auth_flow():
    """Test the authentication flow"""
    print("=" * 60)
    print("Testing Locust Authentication Flow")
    print("=" * 60)
    
    # Simulate the flow
    mock = MockExtractedVars()
    
    # 1. Before login - try to format auth header
    auth_header = "Bearer {token}"
    result = mock.format_string_with_variables(auth_header)
    print(f"\n1. Before login:")
    print(f"   Template: {auth_header}")
    print(f"   Result: '{result}' (should be empty)")
    assert result == "", "Should return empty string when token missing"
    
    # 2. Simulate login response
    login_response = {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test",
        "token_type": "bearer",
        "expires_in": 86400
    }
    print(f"\n2. After login:")
    mock.extract_token(login_response)
    
    # 3. After login - try to format auth header
    result = mock.format_string_with_variables(auth_header)
    print(f"   Template: {auth_header}")
    print(f"   Result: '{result[:40]}...' (should have token)")
    assert result.startswith("Bearer eyJ"), "Should have formatted token"
    
    print("\n" + "=" * 60)
    print("[OK] Authentication flow test PASSED")
    print("\nKey fixes implemented:")
    print("1. Support both 'valueFrom' and 'value_from' in extraction config")
    print("2. Return empty string for auth headers when token is missing")
    print("3. Filter out empty headers before sending requests")
    print("4. Changed error logs to debug logs for missing variables")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_auth_flow()
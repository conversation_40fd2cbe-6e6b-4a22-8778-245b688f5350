Fichier de contexte utilisé : configs/localapi_simple.json

===== RéSUMé DU TEST =====
Temps d'exécution total: 2.9575s
Nombre total de requètes: 45
Nombre de threads: 5
Nombre de tÃ¢ches par thread: 9
Nombre total de tÃ¢ches: 45

--- STATISTIQUES PAR ENDPOINT ---

health:
  Nombre: 5
  Min: 0.2634s
  Max: 0.3586s
  Moyenne: 0.2840s
  Médiane: 0.2654s

login:
  Nombre: 5
  Min: 0.3077s
  Max: 0.3119s
  Moyenne: 0.3100s
  Médiane: 0.3102s

validate:
  Nombre: 5
  Min: 0.2673s
  Max: 0.2705s
  Moyenne: 0.2692s
  Médiane: 0.2696s

profile:
  Nombre: 5
  Min: 0.2613s
  Max: 0.2664s
  Moyenne: 0.2639s
  Médiane: 0.2647s

create_item:
  Nombre: 5
  Min: 0.3321s
  Max: 0.3765s
  Moyenne: 0.3530s
  Médiane: 0.3475s

get_item:
  Nombre: 5
  Min: 0.2566s
  Max: 0.2668s
  Moyenne: 0.2633s
  Médiane: 0.2645s

list_items:
  Nombre: 5
  Min: 0.2921s
  Max: 0.3312s
  Moyenne: 0.3106s
  Médiane: 0.3136s

search:
  Nombre: 5
  Min: 0.3878s
  Max: 0.5760s
  Moyenne: 0.4979s
  Médiane: 0.5134s

analytics:
  Nombre: 5
  Min: 0.2535s
  Max: 0.2691s
  Moyenne: 0.2652s
  Médiane: 0.2673s

--- STATISTIQUES PAR THREAD ---
Thread 2: 9 requètes en 2.8335s
Thread 4: 9 requètes en 2.6908s
Thread 3: 9 requètes en 2.7385s
Thread 1: 9 requètes en 2.8669s
Thread 0: 9 requètes en 2.9553s

--- STATISTIQUES DES CODES HTTP ---

SuccÃ¨s (2xx):
  200: 45 occurrences

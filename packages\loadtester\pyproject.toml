[build-system]
requires = ["hatchling>=1.21.0"]
build-backend = "hatchling.build"

[project]
name = "test-charge"
version = "1.0.0"
description = "Production-ready HTTP Load Testing Framework with asyncio and Locust support"
readme = "README.md"
license = { text = "MIT" }
authors = [
    { name = "Test.Charge Team", email = "<EMAIL>" }
]
keywords = [
    "load-testing",
    "performance-testing", 
    "http-testing",
    "asyncio",
    "locust",
    "api-testing"
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Environment :: Console",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Software Development :: Testing",
    "Topic :: Software Development :: Testing :: Traffic Generation",
    "Topic :: System :: Benchmark"
]
requires-python = ">=3.11"
dependencies = [
    # Core dependencies
    "httpx>=0.28.1",
    "pydantic>=2.5.0",
    "aiofiles>=24.1.0",
    # Testing framework
    "locust>=2.20.0",
    # Utility libraries
    "click>=8.1.0",
    "rich>=13.7.0",
    "pyyaml>=6.0.1",
    "pandas>=2.3.2",
    "matplotlib>=3.10.5",
    "fastapi>=0.116.1",
    "uvicorn>=0.35.0",
    "python-multipart>=0.0.20",
    "pyjwt>=2.10.1",
]

[project.optional-dependencies]
# Full feature set
all = [
    "test-charge[monitoring,dev]"
]

# Monitoring and metrics
monitoring = [
    "prometheus-client>=0.19.0",
    "psutil>=5.9.0",
    "slack-sdk>=3.26.0"
]

# Development dependencies
dev = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.12.0",
    "black>=24.0.0",
    "ruff>=0.2.0",
    "mypy>=1.8.0",
    "pre-commit>=3.6.0"
]

# Documentation
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.5.0",
    "mkdocstrings[python]>=0.24.0"
]

[project.scripts]
test-charge = "test_charge.main:main"

[project.urls]
Homepage = "https://github.com/your-org/test-charge"
Documentation = "https://test-charge.readthedocs.io"
Repository = "https://github.com/your-org/test-charge.git"
Issues = "https://github.com/your-org/test-charge/issues"
Changelog = "https://github.com/your-org/test-charge/blob/main/CHANGELOG.md"

# Hatch configuration
[tool.hatch.build.targets.wheel]
packages = ["src/test_charge"]

[tool.hatch.version]
path = "src/test_charge/__init__.py"

# Testing configuration
[tool.pytest.ini_options]
minversion = "6.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=test_charge",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml"
]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests"
]

# Coverage configuration
[tool.coverage.run]
source = ["src/test_charge"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod"
]

# Code formatting with Black
[tool.black]
line-length = 88
target-version = ['py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# Linting with Ruff
[tool.ruff]
target-version = "py311"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "N",  # pep8-naming
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"tests/*" = ["B011"]

[tool.ruff.isort]
known-first-party = ["test_charge"]

# Type checking with MyPy
[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
warn_unused_configs = true
strict_optional = true

[[tool.mypy.overrides]]
module = [
    "locust.*",
    "prometheus_client.*"
]
ignore_missing_imports = true

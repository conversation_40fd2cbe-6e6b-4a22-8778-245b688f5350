#!/usr/bin/env python
"""
Demo script showing the load tester working against the local FastAPI server.
Run the FastAPI server first: python test_api.py
Then run this demo: python run_demo.py
"""

import subprocess
import time
import sys
import os

def main():
    print("=" * 60)
    print("LOAD TESTER DEMO")
    print("=" * 60)
    
    # Check if FastAPI server is running
    print("\n1. Checking if FastAPI server is running on port 8001...")
    try:
        import httpx
        response = httpx.get("http://localhost:8001/health")
        if response.status_code == 200:
            print("[OK] Server is running!")
        else:
            print("[ERROR] Server returned status:", response.status_code)
            print("Please start the server: python test_api.py")
            return
    except Exception as e:
        print("[ERROR] Server is not running. Please start it first:")
        print("  python test_api.py")
        return
    
    print("\n2. Available test modes:")
    print("   a) Basic asyncio mode (original)")
    print("   b) Enhanced asyncio mode (smoke test)")
    print("   c) Enhanced asyncio mode (stress test)")
    
    choice = input("\nSelect test mode (a/b/c): ").lower()
    
    if choice == 'a':
        print("\n3. Running basic asyncio test...")
        cmd = [
            sys.executable, "-m", "src.main",
            "-c", "configs/localapi_simple.json",
            "--skip-jwt-validation"
        ]
    elif choice == 'b':
        print("\n3. Running enhanced smoke test...")
        cmd = [
            sys.executable, "-m", "src.main",
            "-c", "configs/localapi_simple.json",
            "--mode", "asyncio",
            "--test-type", "smoke",
            "--asyncio-users", "3",
            "--asyncio-duration", "10",
            "--skip-jwt-validation"
        ]
    elif choice == 'c':
        print("\n3. Running enhanced stress test...")
        cmd = [
            sys.executable, "-m", "src.main",
            "-c", "configs/localapi_simple.json",
            "--mode", "asyncio",
            "--test-type", "stress",
            "--asyncio-users", "10",
            "--asyncio-duration", "30",
            "--skip-jwt-validation"
        ]
    else:
        print("Invalid choice!")
        return
    
    print(f"\nCommand: {' '.join(cmd)}")
    print("-" * 60)
    
    # Run the test
    try:
        result = subprocess.run(cmd, capture_output=False, text=True)
        if result.returncode == 0:
            print("\n[OK] Test completed successfully!")
        else:
            print("\n[ERROR] Test failed with return code:", result.returncode)
    except Exception as e:
        print(f"\n[ERROR] Error running test: {e}")
    
    print("\n" + "=" * 60)
    print("DEMO COMPLETE")
    print("=" * 60)
    
    print("\nKey features demonstrated:")
    print("• JWT authentication flow")
    print("• Multiple HTTP methods (GET, POST)")
    print("• Request chaining with dependencies")
    print("• Data extraction and reuse")
    print("• Concurrent user simulation")
    print("• Performance metrics collection")
    
    print("\nTo view detailed reports, check:")
    print("• reports/ directory for test reports")
    print("• logs/ directory for error logs")

if __name__ == "__main__":
    main()
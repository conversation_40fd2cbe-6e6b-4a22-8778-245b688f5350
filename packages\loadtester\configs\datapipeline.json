{"base_url": "http://localhost:3000", "verify_ssl": false, "num_threads": 10, "num_users": 20, "duration": 300, "jwt_token": "", "endpoints": [{"id": "health_check", "url": "/health", "method": "GET", "description": "Health check endpoint", "weight": 5}, {"id": "login", "url": "/auth/login", "method": "POST", "description": "Login to get JWT token", "data": {"username": "testuser", "password": "testpass123"}, "extract": [{"key": "token", "valueFrom": "access_token"}], "weight": 1}, {"id": "get_kgx_data", "url": "/api/v1/kgx/data", "method": "GET", "description": "Get KGX data", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "weight": 10}, {"id": "get_kgx_dashboard", "url": "/api/v1/kgx/dashboard", "method": "GET", "description": "Get KGX dashboard data", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "weight": 8}, {"id": "get_kgx_historical", "url": "/api/v1/kgx/historical?days=7", "method": "GET", "description": "Get historical KGX data", "depends_on": "login", "headers": {"Authorization": "Bearer {token}"}, "weight": 5}, {"id": "process_pipeline", "url": "/api/processing/trigger", "method": "POST", "description": "Trigger data pipeline processing", "depends_on": "login", "headers": {"Authorization": "Bearer {token}", "Content-Type": "application/json"}, "data": {"pipelineId": "kgx-daily-update", "parameters": {}}, "weight": 2}, {"id": "get_pipeline_status", "url": "/api/processing/status/{jobId}", "method": "GET", "description": "Get pipeline processing status", "depends_on": "process_pipeline", "extract": [{"key": "jobId", "valueFrom": "jobId"}], "headers": {"Authorization": "Bearer {token}"}, "weight": 3}], "test_scenarios": {"smoke": {"description": "Basic smoke test with minimal load", "duration": 60, "users": 5, "spawn_rate": 1, "endpoints": [{"id": "health_check", "weight": 5}, {"id": "get_kgx_data", "weight": 3}, {"id": "get_kgx_dashboard", "weight": 2}]}, "standard": {"description": "Standard load test with mixed operations", "duration": 300, "users": 20, "spawn_rate": 2, "endpoints": [{"id": "get_kgx_data", "weight": 5}, {"id": "get_kgx_dashboard", "weight": 4}, {"id": "get_kgx_historical", "weight": 3}, {"id": "process_pipeline", "weight": 1}]}, "stress": {"description": "Stress test with high concurrency", "duration": 600, "users": 50, "spawn_rate": 5, "endpoints": [{"id": "get_kgx_data", "weight": 6}, {"id": "get_kgx_dashboard", "weight": 3}, {"id": "process_pipeline", "weight": 1}]}, "pipeline_heavy": {"description": "Focus on pipeline processing operations", "duration": 300, "users": 10, "spawn_rate": 1, "endpoints": [{"id": "process_pipeline", "weight": 8}, {"id": "get_pipeline_status", "weight": 2}]}}}
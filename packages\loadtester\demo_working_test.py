#!/usr/bin/env python
"""
Demo script showing successful load testing against the local FastAPI server.
This demonstrates how the load tester works when properly configured.
"""

import httpx
import asyncio
import time
import json
from datetime import datetime
import statistics

class SimpleLoadTester:
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.token = None
        self.results = []
        
    async def login(self):
        """Login and get JWT token"""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/auth/login",
                json={"username": "testuser", "password": "testpass123"}
            )
            if response.status_code == 200:
                data = response.json()
                self.token = data["access_token"]
                print(f"[OK] Login successful, token obtained")
                return True
            else:
                print(f"[FAIL] Login failed: {response.status_code}")
                return False
    
    async def test_endpoint(self, method, path, headers=None, json_data=None):
        """Test a single endpoint"""
        url = f"{self.base_url}{path}"
        
        async with httpx.AsyncClient() as client:
            start_time = time.time()
            try:
                if method == "GET":
                    response = await client.get(url, headers=headers)
                elif method == "POST":
                    response = await client.post(url, headers=headers, json=json_data)
                elif method == "PUT":
                    response = await client.put(url, headers=headers, json=json_data)
                else:
                    response = await client.request(method, url, headers=headers, json=json_data)
                
                elapsed = time.time() - start_time
                
                return {
                    "endpoint": path,
                    "method": method,
                    "status": response.status_code,
                    "time": elapsed,
                    "success": 200 <= response.status_code < 300
                }
            except Exception as e:
                elapsed = time.time() - start_time
                return {
                    "endpoint": path,
                    "method": method,
                    "status": -1,
                    "time": elapsed,
                    "success": False,
                    "error": str(e)
                }
    
    async def run_test_sequence(self, thread_id):
        """Run a sequence of tests simulating a user session"""
        headers = {"Authorization": f"Bearer {self.token}"} if self.token else {}
        
        # Test sequence
        tests = [
            ("GET", "/health", {}),
            ("GET", "/api/user/profile", headers),
            ("POST", "/api/items", headers, {
                "name": f"Test Item {thread_id}",
                "description": "Load test item",
                "price": 99.99,
                "quantity": 10
            }),
            ("GET", "/api/items", headers),
            ("POST", "/api/search", headers, {"query": "test"}),
            ("GET", "/api/analytics/summary", headers),
        ]
        
        thread_results = []
        for method, path, test_headers, *data in tests:
            json_data = data[0] if data else None
            result = await self.test_endpoint(method, path, test_headers, json_data)
            result["thread"] = thread_id
            thread_results.append(result)
            
            # Small delay between requests
            await asyncio.sleep(0.1)
        
        return thread_results
    
    async def run_load_test(self, num_threads=5):
        """Run load test with multiple concurrent threads"""
        print(f"\n[START] Starting load test with {num_threads} concurrent users")
        print("=" * 60)
        
        # Login first
        if not await self.login():
            print("Failed to login, aborting test")
            return
        
        # Run concurrent tests
        start_time = time.time()
        tasks = [self.run_test_sequence(i) for i in range(num_threads)]
        all_results = await asyncio.gather(*tasks)
        
        # Flatten results
        for thread_results in all_results:
            self.results.extend(thread_results)
        
        total_time = time.time() - start_time
        
        # Print results
        self.print_results(total_time)
    
    def print_results(self, total_time):
        """Print test results summary"""
        print("\n" + "=" * 60)
        print("TEST RESULTS")
        print("=" * 60)
        print(f"Total execution time: {total_time:.2f}s")
        print(f"Total requests: {len(self.results)}")
        
        # Group by endpoint
        endpoints = {}
        for result in self.results:
            key = f"{result['method']} {result['endpoint']}"
            if key not in endpoints:
                endpoints[key] = []
            endpoints[key].append(result)
        
        print("\n--- ENDPOINT STATISTICS ---")
        for endpoint, results in endpoints.items():
            times = [r['time'] for r in results]
            successes = sum(1 for r in results if r['success'])
            
            print(f"\n{endpoint}:")
            print(f"  Requests: {len(results)}")
            print(f"  Success rate: {successes}/{len(results)} ({100*successes/len(results):.1f}%)")
            print(f"  Min time: {min(times):.3f}s")
            print(f"  Max time: {max(times):.3f}s")
            print(f"  Avg time: {statistics.mean(times):.3f}s")
            if len(times) > 1:
                print(f"  Median: {statistics.median(times):.3f}s")
        
        # Overall statistics
        all_times = [r['time'] for r in self.results]
        all_successes = sum(1 for r in self.results if r['success'])
        
        print("\n--- OVERALL STATISTICS ---")
        print(f"Success rate: {all_successes}/{len(self.results)} ({100*all_successes/len(self.results):.1f}%)")
        print(f"Average response time: {statistics.mean(all_times):.3f}s")
        print(f"Throughput: {len(self.results)/total_time:.1f} requests/second")
        
        # Error summary
        errors = [r for r in self.results if not r['success']]
        if errors:
            print("\n--- ERRORS ---")
            error_counts = {}
            for error in errors:
                key = f"{error['method']} {error['endpoint']} (Status: {error['status']})"
                error_counts[key] = error_counts.get(key, 0) + 1
            
            for error_type, count in error_counts.items():
                print(f"  {error_type}: {count} occurrences")

async def main():
    print("=" * 60)
    print("LOAD TESTER DEMO - Working Example")
    print("=" * 60)
    
    # Check if server is running
    print("\nChecking if FastAPI server is running...")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:8001/health")
            if response.status_code == 200:
                print("[OK] Server is running!")
            else:
                print("[FAIL] Server returned unexpected status:", response.status_code)
                return
    except Exception as e:
        print("[FAIL] Server is not running. Please start it first:")
        print("  python test_api.py")
        return
    
    # Run tests with different concurrency levels
    for num_users in [1, 5, 10]:
        tester = SimpleLoadTester()
        await tester.run_load_test(num_threads=num_users)
        print("\n" + "=" * 60)
        await asyncio.sleep(2)  # Brief pause between tests
    
    print("\n[COMPLETE] Demo complete!")
    print("\nThis demonstrates:")
    print("• JWT authentication flow")
    print("• Multiple HTTP methods (GET, POST)")
    print("• Concurrent user simulation")
    print("• Performance metrics collection")
    print("• Error handling and reporting")

if __name__ == "__main__":
    asyncio.run(main())
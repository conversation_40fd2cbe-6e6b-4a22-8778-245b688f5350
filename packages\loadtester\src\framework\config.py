import json
import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class TestConfig:
    """Configuration data class for load tests"""
    base_url: str
    jwt_token: str
    verify_ssl: bool = True
    num_users: int = 10
    spawn_rate: float = 1.0
    run_time: str = "1m"
    
    # Connection settings
    connection_timeout: float = 30.0
    read_timeout: float = 60.0
    max_retries: int = 3
    
    # Test behavior
    wait_time_min: float = 1.0
    wait_time_max: float = 3.0
    
    # Endpoints configuration
    endpoints: List[Dict[str, Any]] = field(default_factory=list)
    
    # Monitoring settings
    enable_monitoring: bool = True
    metrics_interval: int = 5
    log_slow_requests: bool = True
    slow_request_threshold: float = 5000.0
    
    # Reporting settings
    report_format: str = "html"  # html, csv, json
    report_dir: str = "reports"
    
    # Advanced settings
    custom_headers: Dict[str, str] = field(default_factory=dict)
    test_data: Dict[str, Any] = field(default_factory=dict)


class ConfigManager:
    """Manages configuration loading and merging for load tests"""
    
    def __init__(self, base_dir: Optional[Path] = None):
        self.base_dir = base_dir or Path.cwd()
        self.configs_dir = self.base_dir / "configs"
        self.global_config_path = self.base_dir / "config.global.json"
        
    def load_config(self, context: Optional[str] = None, 
                   environment: Optional[str] = None,
                   overrides: Optional[Dict[str, Any]] = None) -> TestConfig:
        """
        Load and merge configuration from multiple sources
        
        Args:
            context: Context-specific config (e.g., 'andoc', 'talperftraitement')
            environment: Environment config (e.g., 'dev', 'staging', 'prod')
            overrides: Runtime configuration overrides
            
        Returns:
            TestConfig: Merged configuration object
        """
        config_data = {}
        
        # 1. Load global configuration
        if self.global_config_path.exists():
            with open(self.global_config_path, 'r', encoding='utf-8') as f:
                global_config = json.load(f)
                config_data.update(global_config)
                logger.info(f"Loaded global config from {self.global_config_path}")
        
        # 2. Load environment-specific configuration
        if environment:
            env_config_path = self.configs_dir / "environments.json"
            if env_config_path.exists():
                with open(env_config_path, 'r', encoding='utf-8') as f:
                    env_configs = json.load(f)
                    if environment in env_configs:
                        config_data.update(env_configs[environment])
                        logger.info(f"Loaded environment config: {environment}")
        
        # 3. Load context-specific configuration
        if context:
            context_config_path = self.configs_dir / f"{context}.json"
            if context_config_path.exists():
                with open(context_config_path, 'r', encoding='utf-8') as f:
                    context_config = json.load(f)
                    config_data.update(context_config)
                    logger.info(f"Loaded context config: {context}")
        
        # 4. Apply runtime overrides
        if overrides:
            config_data.update(overrides)
            logger.info("Applied runtime overrides")
        
        # 5. Load from environment variables
        self._load_env_variables(config_data)
        
        # 6. Validate and create TestConfig
        return self._create_test_config(config_data)
    
    def _load_env_variables(self, config_data: Dict[str, Any]) -> None:
        """Load configuration from environment variables"""
        env_mappings = {
            'LOAD_TEST_BASE_URL': 'base_url',
            'LOAD_TEST_JWT_TOKEN': 'jwt_token',
            'LOAD_TEST_NUM_USERS': 'num_users',
            'LOAD_TEST_SPAWN_RATE': 'spawn_rate',
            'LOAD_TEST_RUN_TIME': 'run_time',
            'LOAD_TEST_VERIFY_SSL': 'verify_ssl'
        }
        
        for env_var, config_key in env_mappings.items():
            if env_var in os.environ:
                value = os.environ[env_var]
                # Type conversion
                if config_key in ['num_users']:
                    value = int(value)
                elif config_key in ['spawn_rate']:
                    value = float(value)
                elif config_key in ['verify_ssl']:
                    value = value.lower() in ('true', '1', 'yes', 'on')
                
                config_data[config_key] = value
                logger.info(f"Loaded {config_key} from environment variable")
    
    def _create_test_config(self, config_data: Dict[str, Any]) -> TestConfig:
        """Create TestConfig object from configuration data"""
        # Set defaults for required fields
        defaults = {
            'base_url': '',
            'jwt_token': '',
            'verify_ssl': True,
            'num_users': 10,
            'spawn_rate': 1.0,
            'run_time': '1m',
            'connection_timeout': 30.0,
            'read_timeout': 60.0,
            'max_retries': 3,
            'wait_time_min': 1.0,
            'wait_time_max': 3.0,
            'endpoints': [],
            'enable_monitoring': True,
            'metrics_interval': 5,
            'log_slow_requests': True,
            'slow_request_threshold': 5000.0,
            'report_format': 'html',
            'report_dir': 'reports',
            'custom_headers': {},
            'test_data': {}
        }
        
        # Apply defaults first
        merged_config = defaults.copy()
        merged_config.update(config_data)
        
        # Extract known fields
        known_fields = {
            field.name for field in TestConfig.__dataclass_fields__.values()
        }
        
        test_config_data = {
            key: value for key, value in merged_config.items() 
            if key in known_fields
        }
        
        # Store unknown fields in test_data
        unknown_data = {
            key: value for key, value in merged_config.items() 
            if key not in known_fields and key not in defaults
        }
        
        if unknown_data:
            if 'test_data' not in test_config_data:
                test_config_data['test_data'] = {}
            test_config_data['test_data'].update(unknown_data)
        
        return TestConfig(**test_config_data)
    
    def save_config(self, config: TestConfig, path: Path) -> None:
        """Save configuration to file"""
        config_dict = {
            'base_url': config.base_url,
            'jwt_token': config.jwt_token,
            'verify_ssl': config.verify_ssl,
            'num_users': config.num_users,
            'spawn_rate': config.spawn_rate,
            'run_time': config.run_time,
            'connection_timeout': config.connection_timeout,
            'read_timeout': config.read_timeout,
            'max_retries': config.max_retries,
            'wait_time_min': config.wait_time_min,
            'wait_time_max': config.wait_time_max,
            'endpoints': config.endpoints,
            'enable_monitoring': config.enable_monitoring,
            'metrics_interval': config.metrics_interval,
            'log_slow_requests': config.log_slow_requests,
            'slow_request_threshold': config.slow_request_threshold,
            'report_format': config.report_format,
            'report_dir': config.report_dir,
            'custom_headers': config.custom_headers,
            **config.test_data
        }
        
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Configuration saved to {path}")
    
    def validate_config(self, config: TestConfig) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        if not config.base_url:
            errors.append("base_url is required")
        
        if not config.jwt_token:
            errors.append("jwt_token is required")
        
        if config.num_users <= 0:
            errors.append("num_users must be positive")
        
        if config.spawn_rate <= 0:
            errors.append("spawn_rate must be positive")
        
        if not config.endpoints:
            errors.append("At least one endpoint must be configured")
        
        return errors
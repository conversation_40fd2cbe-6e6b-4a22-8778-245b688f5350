import asyncio
import argparse
import os
import sys
import subprocess
from pathlib import Path
from enum import Enum

from .core.utils import get_config_paths
from .core.processor import (
    process_test_cases,
    asyncio_process_test_cases,
    print_test_type_help,
)


class TestMode(Enum):
    ASYNCIO = "asyncio"
    LOCUST = "locust"


def main():
    parser = argparse.ArgumentParser(
        description="Application de test de charge HTTP avec support Locust",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Modes de test disponibles:
  asyncio (dfaut)  - Mode Test.Charge avec asyncio (original + enhanced)
  locust           - Mode Locust pour tests de charge avancs

Types de test (asyncio et locust):
  standard  - Test de charge quilibr avec concurrence modre
  stress    - Test haute intensit pour trouver les limites
  smoke     - Validation lgre avec charge minimale
  chaos     - Patterns de charge imprvisibles

Exemples:
  # Mode asyncio original
  python -m src.main -c config.json --mode asyncio
  
  # Mode asyncio enhanced avec type de test
  python -m src.main -c config.json --mode asyncio --test-type stress --asyncio-users 50 --asyncio-duration 300
  
  # Mode Locust avec interface web
  python -m src.main -c config.json --mode locust --locust-ui
  
  # Test de stress avec Locust
  python -m src.main -c config.json --mode locust --locust-test-type stress --users 100 --headless
        """,
    )

    # Configuration de base
    parser.add_argument(
        "-c",
        "--config",
        required=True,
        help="Chemin vers le fichier de configuration JSON ou contexte (ex: 'andoc')",
    )

    parser.add_argument(
        "--mode",
        choices=[mode.value for mode in TestMode],
        default=TestMode.ASYNCIO.value,
        type=str,
        help="Mode de test: asyncio (original) ou locust (dfaut: asyncio)",
    )

    parser.add_argument(
        "--skip-jwt-validation",
        action="store_true",
        help="Ignorer la validation du jeton JWT",
    )

    # Arguments pour le mode asyncio enhanced
    asyncio_group = parser.add_argument_group("Options Asyncio Enhanced")

    asyncio_group.add_argument(
        "--test-type",
        choices=["original", "standard", "stress", "smoke", "chaos"],
        default="original",
        help="Type de test asyncio: original (mode classique) ou enhanced (standard/stress/smoke/chaos)",
    )

    asyncio_group.add_argument(
        "--asyncio-users",
        type=int,
        help="Nombre d'utilisateurs concurrents pour le mode enhanced (remplace num_threads)",
    )

    asyncio_group.add_argument(
        "--asyncio-duration",
        type=float,
        help="Dure du test en secondes pour le mode enhanced",
    )

    asyncio_group.add_argument(
        "--ramp-up-time", type=float, help="Temps de monte en charge en secondes"
    )

    asyncio_group.add_argument(
        "--think-time-min",
        type=float,
        help="Temps d'attente minimum entre les requtes (secondes)",
    )

    asyncio_group.add_argument(
        "--think-time-max",
        type=float,
        help="Temps d'attente maximum entre les requtes (secondes)",
    )

    asyncio_group.add_argument(
        "--csv-report", help="Nom du fichier CSV pour le rapport dtaill"
    )

    asyncio_group.add_argument(
        "--json-report", help="Nom du fichier JSON pour le rapport dtaill"
    )

    asyncio_group.add_argument(
        "--help-test-types",
        action="store_true",
        help="Afficher l'aide dtaille sur les types de test",
    )

    # Arguments spcifiques   Locust
    locust_group = parser.add_argument_group("Options Locust")

    locust_group.add_argument(
        "--host", help="URL cible pour les tests Locust (ex: https://api.example.com)"
    )

    locust_group.add_argument(
        "--users", "-u", type=int, help="Nombre d'utilisateurs concurrents pour Locust"
    )

    locust_group.add_argument(
        "--spawn-rate",
        "-r",
        type=float,
        help="Taux de cration d'utilisateurs par seconde",
    )

    locust_group.add_argument(
        "--run-time", "-t", help="Dure du test (ex: 10s, 5m, 1h)"
    )

    locust_group.add_argument(
        "--headless", action="store_true", help="Mode Locust sans interface web"
    )

    locust_group.add_argument(
        "--locust-ui",
        action="store_true",
        help="Forcer l'utilisation de l'interface web Locust",
    )

    locust_group.add_argument(
        "--locust-test-type",
        choices=["standard", "stress", "smoke", "chaos"],
        default="standard",
        help="Type de test Locust",
    )

    locust_group.add_argument(
        "--web-port",
        type=int,
        default=8089,
        help="Port pour l'interface web Locust (dfaut: 8089)",
    )

    locust_group.add_argument(
        "--csv-output", help="Prfixe pour les fichiers CSV de rsultats"
    )

    locust_group.add_argument(
        "--html-output", help="Fichier HTML pour le rapport de rsultats"
    )

    # Arguments de monitoring
    monitoring_group = parser.add_argument_group("Monitoring et Intgrations")

    monitoring_group.add_argument(
        "--prometheus-url", help="URL Prometheus Pushgateway pour les mtriques"
    )

    monitoring_group.add_argument(
        "--slack-webhook", help="Webhook Slack pour les alertes"
    )

    args = parser.parse_args()
    mode = args.mode  # Keep the string value

    # Handle help for test types
    if hasattr(args, "help_test_types") and args.help_test_types:
        print_test_type_help()
        sys.exit(0)

    try:
        if mode == TestMode.ASYNCIO.value:
            if args.test_type == "original":
                # Mode original Test.Charge
                print(" Lancement du mode Test.Charge original (asyncio)")
                asyncio.run(process_test_cases(args.config, args.skip_jwt_validation))
            else:
                # Mode asyncio enhanced
                print(
                    f" Lancement du mode Test.Charge Enhanced (asyncio - {args.test_type})"
                )
                asyncio.run(run_enhanced_asyncio_mode(args))

        elif mode == TestMode.LOCUST.value:
            # Mode Locust intgr
            print(" Lancement du mode Test.Charge avec Locust")
            run_locust_mode(args)

    except KeyboardInterrupt:
        print("\n Test interrompu par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f" Erreur: {e}")
        sys.exit(1)


async def run_enhanced_asyncio_mode(args):
    """Lance le mode asyncio enhanced avec les paramtres spcifis"""

    # Prparer les paramtres utilisateur
    user_params = {}

    if args.asyncio_users:
        user_params["concurrency"] = args.asyncio_users

    if args.asyncio_duration:
        user_params["duration"] = args.asyncio_duration

    if args.ramp_up_time:
        user_params["ramp_up_time"] = args.ramp_up_time

    if args.think_time_min:
        user_params["think_time_min"] = args.think_time_min

    if args.think_time_max:
        user_params["think_time_max"] = args.think_time_max

    # Afficher la configuration
    print(f" Configuration du test Enhanced Asyncio - {args.test_type.upper()}")
    print("=" * 50)

    if user_params:
        for key, value in user_params.items():
            print(f"  {key}: {value}")
    else:
        print("  Utilisation des paramtres par dfaut du type de test")

    print("=" * 50)
    print()

    # Excuter le test enhanced
    success = await asyncio_process_test_cases(
        config_path=args.config,
        test_type=args.test_type,
        skip_jwt_validation=args.skip_jwt_validation,
        user_params=user_params,
    )

    if not success:
        print(" Test termin avec des erreurs")
        sys.exit(1)


def run_locust_mode(args):
    """Lance le mode Locust avec les paramtres spcifis"""

    # Dterminer le contexte   partir du fichier de config
    context = determine_context_from_config(args.config)

    # Configurer les variables d'environnement
    setup_locust_environment(args, context)

    # Construire la commande Locust
    locust_cmd = build_locust_command(args)

    # Afficher les informations de lancement
    print_locust_info(args, context, locust_cmd)

    # Excuter Locust
    try:
        subprocess.run(locust_cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f" chec de l'excution Locust avec le code de sortie {e.returncode}")
        sys.exit(e.returncode)
    except FileNotFoundError:
        print(" Locust non trouv. Installez avec: pip install locust")
        sys.exit(1)


def determine_context_from_config(config_path: str) -> str:
    """Dtermine le contexte   partir du chemin de configuration"""

    # Si c'est juste un nom (ex: 'andoc'), c'est le contexte
    if (
        not config_path.endswith(".json")
        and "/" not in config_path
        and "\\" not in config_path
    ):
        return config_path

    # Si c'est un chemin vers un fichier de contexte
    path = Path(config_path)
    if path.name.startswith("config.") and path.name.endswith(".json"):
        # C'est probablement un fichier config fusionn, essayer de deviner le contexte
        return "default"

    # Extraire le contexte du nom de fichier
    if path.name != "config.global.json":
        return path.stem

    return "default"


def setup_locust_environment(args, context: str):
    """Configure les variables d'environnement pour Locust"""

    # Configuration Test.Charge
    os.environ["CONFIG_CONTEXT"] = context

    if hasattr(args, "locust_test_type") and args.locust_test_type:
        os.environ["TEST_TYPE"] = args.locust_test_type
    elif hasattr(args, "test_type") and args.test_type != "original":
        # Fallback for backward compatibility
        os.environ["TEST_TYPE"] = args.test_type

    if args.skip_jwt_validation:
        os.environ["SKIP_JWT_VALIDATION"] = "true"

    # Monitoring
    if args.prometheus_url:
        os.environ["PROMETHEUS_PUSHGATEWAY_URL"] = args.prometheus_url

    if args.slack_webhook:
        os.environ["SLACK_WEBHOOK_URL"] = args.slack_webhook


def build_locust_command(args) -> list:
    """Construit la commande Locust   excuter"""

    # Load config to get base_url for host
    try:
        global_config_path, context_config_path = get_config_paths(args.config)

        # Load and merge configs
        import json

        with open(global_config_path, "r") as gf:
            global_config = json.load(gf)
        with open(context_config_path, "r") as cf:
            context_config = json.load(cf)

        config = global_config.copy()
        config.update(context_config)

    except Exception as e:
        print(f"  Warning: Could not load config for host detection: {e}")
        config = {}

    # Trouver le fichier locustfile
    script_dir = Path(__file__).parent

    # # Use simple locustfile for now until full integration is stable
    # locustfile_path = script_dir / "locust" / "simple_locustfile.py"

    # if not locustfile_path.exists():
    #     # Fallback to full locustfile
    #     locustfile_path = script_dir / "locust" / "locustfile.py"

    locustfile_path = script_dir / "locust" / "locustfile.py"

    if not locustfile_path.exists():
        raise FileNotFoundError(f"Fichier locustfile non trouv: {locustfile_path}")

    cmd = ["locust", "-f", str(locustfile_path)]

    # # URL hte
    # if args.host:
    #     cmd.extend(["--host", args.host])

    # Host URL - priorit: CLI argument > config base_url
    host_url = None
    if args.host:
        host_url = args.host
    elif config.get("base_url"):
        host_url = config["base_url"]

    if host_url:
        cmd.extend(["--host", host_url])
        print(f" Host configur: {host_url}")
    else:
        print(
            "  Warning: Aucun host configur - vous devrez le spcifier dans l'interface web"
        )

    # Paramtres d'utilisateurs
    if args.users:
        cmd.extend(["-u", str(args.users)])

    if args.spawn_rate:
        cmd.extend(["-r", str(args.spawn_rate)])

    # Dure du test - Validation et formatage
    if args.run_time:
        formatted_time = format_locust_runtime(args.run_time)
        cmd.extend(["-t", formatted_time])

    # Mode headless vs UI
    if args.headless and not args.locust_ui:
        cmd.append("--headless")

    # Port web
    if not args.headless or args.locust_ui:
        cmd.extend(["--web-port", str(args.web_port)])

    # Sorties
    if args.csv_output:
        cmd.extend(["--csv", args.csv_output])

    if args.html_output:
        cmd.extend(["--html", args.html_output])

    return cmd


def format_locust_runtime(runtime_str: str) -> str:
    """Format runtime string for Locust compatibility"""
    runtime_str = runtime_str.strip()

    # If it's just a number, assume seconds
    if runtime_str.isdigit():
        return f"{runtime_str}s"

    # If it already has a suffix, validate and return
    valid_suffixes = ["s", "m", "h"]
    if any(runtime_str.endswith(suffix) for suffix in valid_suffixes):
        return runtime_str

    # Try to parse common formats
    if runtime_str.endswith("min"):
        return runtime_str.replace("min", "m")
    elif runtime_str.endswith("sec"):
        return runtime_str.replace("sec", "s")
    elif runtime_str.endswith("hour") or runtime_str.endswith("hr"):
        return runtime_str.replace("hour", "h").replace("hr", "h")

    # Default to seconds if unclear
    return f"{runtime_str}s"


def print_locust_info(args, context: str, locust_cmd: list):
    """Affiche les informations sur le lancement Locust"""

    print(" Configuration du test Locust")
    print("=" * 40)
    print(f" Contexte: {context}")
    test_type = getattr(
        args, "locust_test_type", getattr(args, "test_type", "standard")
    )
    if test_type == "original":
        test_type = "standard"
    print(f" Type de test: {test_type}")

    if args.host:
        print(f" Hte cible: {args.host}")

    if args.users:
        print(f" Utilisateurs: {args.users}")

    if args.spawn_rate:
        print(f" Taux de spawn: {args.spawn_rate}/sec")

    if args.run_time:
        print(f" Dure: {args.run_time}")

    mode = "Interface Web" if not args.headless or args.locust_ui else "Mode Headless"
    print(f" Mode: {mode}")

    if not args.headless or args.locust_ui:
        print(f" Interface web: http://localhost:{args.web_port}")

    print("=" * 40)
    print(f" Commande: {' '.join(locust_cmd)}")
    print("")

    if not args.headless:
        print(" Lancement de l'interface web Locust...")
        print("   Accdez   l'interface pour configurer et dmarrer le test")
        print("   Appuyez sur Ctrl+C pour arrter")
    else:
        print(" Lancement du test automatis...")

    print("")


def show_available_contexts():
    """Affiche les contextes disponibles"""

    configs_dir = Path(__file__).parent.parent / "configs"
    if not configs_dir.exists():
        print("Aucun rpertoire configs trouv")
        return

    print("Contextes disponibles:")
    for config_file in configs_dir.glob("*.json"):
        if config_file.name != "environments.json":
            print(f"  - {config_file.stem}")


if __name__ == "__main__":
    main()

Fichier de contexte utilisé : configs/localapi_simple.json

===== RéSUMé DU TEST =====
Temps d'exécution total: 2.9491s
Nombre total de requètes: 45
Nombre de threads: 5
Nombre de tÃ¢ches par thread: 9
Nombre total de tÃ¢ches: 45

--- STATISTIQUES PAR ENDPOINT ---

health:
  Nombre: 5
  Min: 0.2880s
  Max: 0.3908s
  Moyenne: 0.3103s
  Médiane: 0.2899s

login:
  Nombre: 5
  Min: 0.2922s
  Max: 0.3250s
  Moyenne: 0.3045s
  Médiane: 0.3007s

validate:
  Nombre: 5
  Min: 0.2745s
  Max: 0.2830s
  Moyenne: 0.2783s
  Médiane: 0.2780s

profile:
  Nombre: 5
  Min: 0.2702s
  Max: 0.2886s
  Moyenne: 0.2812s
  Médiane: 0.2834s

create_item:
  Nombre: 5
  Min: 0.3072s
  Max: 0.3721s
  Moyenne: 0.3408s
  Médiane: 0.3433s

get_item:
  Nombre: 5
  Min: 0.2672s
  Max: 0.2730s
  Moyenne: 0.2702s
  Médiane: 0.2703s

list_items:
  Nombre: 5
  Min: 0.2860s
  Max: 0.3133s
  Moyenne: 0.2998s
  Médiane: 0.2988s

search:
  Nombre: 5
  Min: 0.3890s
  Max: 0.5436s
  Moyenne: 0.4652s
  Médiane: 0.4762s

analytics:
  Nombre: 5
  Min: 0.2566s
  Max: 0.2653s
  Moyenne: 0.2632s
  Médiane: 0.2649s

--- STATISTIQUES PAR THREAD ---
Thread 1: 9 requètes en 2.7382s
Thread 3: 9 requètes en 2.7680s
Thread 4: 9 requètes en 2.8502s
Thread 2: 9 requètes en 2.7695s
Thread 0: 9 requètes en 2.9415s

--- STATISTIQUES DES CODES HTTP ---

SuccÃ¨s (2xx):
  200: 45 occurrences

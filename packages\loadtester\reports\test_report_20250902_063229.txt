Fichier de contexte utilisé : configs/localapi_simple.json

===== RéSUMé DU TEST =====
Temps d'exécution total: 3.0670s
Nombre total de requètes: 45
Nombre de threads: 5
Nombre de tÃ¢ches par thread: 9
Nombre total de tÃ¢ches: 45

--- STATISTIQUES PAR ENDPOINT ---

health:
  Nombre: 5
  Min: 0.2652s
  Max: 0.3234s
  Moyenne: 0.2781s
  Médiane: 0.2681s

login:
  Nombre: 5
  Min: 0.2886s
  Max: 0.3076s
  Moyenne: 0.2966s
  Médiane: 0.2916s

validate:
  Nombre: 5
  Min: 0.4089s
  Max: 0.4585s
  Moyenne: 0.4450s
  Médiane: 0.4539s

profile:
  Nombre: 5
  Min: 0.2700s
  Max: 0.2965s
  Moyenne: 0.2831s
  Médiane: 0.2844s

create_item:
  Nombre: 5
  Min: 0.3008s
  Max: 0.3846s
  Moyenne: 0.3329s
  Médiane: 0.3251s

get_item:
  Nombre: 5
  Min: 0.2831s
  Max: 0.3206s
  Moyenne: 0.2928s
  Médiane: 0.2853s

list_items:
  Nombre: 5
  Min: 0.2745s
  Max: 0.3085s
  Moyenne: 0.2975s
  Médiane: 0.3043s

search:
  Nombre: 5
  Min: 0.3792s
  Max: 0.4684s
  Moyenne: 0.4171s
  Médiane: 0.4071s

analytics:
  Nombre: 5
  Min: 0.2541s
  Max: 0.2652s
  Moyenne: 0.2619s
  Médiane: 0.2637s

--- STATISTIQUES PAR THREAD ---
Thread 0: 9 requètes en 3.0028s
Thread 3: 9 requètes en 3.0054s
Thread 4: 9 requètes en 2.8376s
Thread 1: 9 requètes en 2.8410s
Thread 2: 9 requètes en 2.8384s

--- STATISTIQUES DES CODES HTTP ---

SuccÃ¨s (2xx):
  200: 45 occurrences

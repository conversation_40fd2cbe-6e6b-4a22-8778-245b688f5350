import time
import statistics
import collections
import io
import datetime
import aiofiles
from pathlib import Path
from typing import List, Optional, Dict

class TimingResult:
    """Stocke les résultats de chronométrage pour un appel HTTP."""

    def __init__(
        self,
        endpoint: str,
        thread_id: int,
        start_time: float,
        end_time: float,
        status_code: int,
        endpoint_id: Optional[str] = None,
    ):
        self.endpoint = endpoint
        self.endpoint_id = (
            endpoint_id or endpoint
        )  # Utilise l'ID personnalisé ou l'URL par défaut
        self.thread_id = thread_id
        self.start_time = start_time
        self.end_time = end_time
        self.duration = end_time - start_time
        self.status_code = status_code

    def __str__(self):
        if self.endpoint_id != self.endpoint:
            return f"Thread {self.thread_id} - {self.endpoint} (ID: {self.endpoint_id}): {self.duration:.4f}s (Status: {self.status_code})"
        return f"Thread {self.thread_id} - {self.endpoint}: {self.duration:.4f}s (Status: {self.status_code})"

class Timer:
    """GÃ¨re le chronométrage global et par appel."""

    def __init__(self):
        self.results: List[TimingResult] = []
        self.global_start: Optional[float] = None
        self.global_end: Optional[float] = None
        # Dictionnaire pour compter les erreurs par code de statut
        self.status_code_counts = collections.defaultdict(int)
        # Dictionnaire pour stocker les URLs par code de statut avec leur nombre d'occurrences
        self.status_urls = collections.defaultdict(lambda: collections.defaultdict(int))
        # Buffer pour stocker la sortie du rapport
        self.report_buffer = io.StringIO()
        # Nombre de threads et tÃ¢ches
        self.num_threads = 0
        self.num_tasks_per_thread = 0
        # Attribut pour le fichier de log d'erreur
        self.error_log_file = None

    def set_thread_and_task_info(self, num_threads: int, num_tasks_per_thread: int):
        """Définit le nombre de threads et de tÃ¢ches par thread."""
        self.num_threads = num_threads
        self.num_tasks_per_thread = num_tasks_per_thread

    def start_global(self):
        self.global_start = time.time()
        # Initialiser le fichier de log d'erreur unique pour ce run
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        self.error_log_file = logs_dir / f"errors_{timestamp}.log"

    def end_global(self):
        self.global_end = time.time()

    def add_result(self, result: TimingResult):
        self.results.append(result)
        # Compter le code de statut
        self.status_code_counts[result.status_code] += 1
        # Stocker l'URL pour ce code de statut s'il s'agit d'une erreur
        if result.status_code >= 400 or result.status_code < 0:
            # Extraire la partie de l'URL avant le "?"
            clean_url = result.endpoint.split("?")[0]
            # Incrémenter le compteur pour cette URL
            self.status_urls[result.status_code][clean_url] += 1

    def _write_to_output(self, text: str):
        """écrit le texteà  la fois dans le buffer du rapport et sur la console."""
        self.report_buffer.write(text + "\n")

    def _print_global_info(self):
        """Affiche les informations globales du test."""
        global_time = (
            self.global_end - self.global_start
            if self.global_start and self.global_end
            else 0
        )

        self._write_to_output("\n===== RéSUMé DU TEST =====")
        self._write_to_output(f"Temps d'exécution total: {global_time:.4f}s")
        self._write_to_output(f"Nombre total de requètes: {len(self.results)}")
        self._write_to_output(f"Nombre de threads: {self.num_threads}")
        self._write_to_output(
            f"Nombre de tÃ¢ches par thread: {self.num_tasks_per_thread}"
        )
        self._write_to_output(
            f"Nombre total de tÃ¢ches: {self.num_threads * self.num_tasks_per_thread}"
        )

    def _print_endpoint_statistics(self):
        """Calcule et affiche les statistiques par endpoint."""
        # Regrouper les durées par endpoint_id
        endpoints_data = self._group_results_by_endpoint()

        self._write_to_output("\n--- STATISTIQUES PAR ENDPOINT ---")
        for endpoint_id, durations in endpoints_data.items():
            self._print_duration_statistics(endpoint_id, durations)

    def _group_results_by_endpoint(self) -> Dict[str, List[float]]:
        """Regroupe les résultats par endpoint_id."""
        endpoints = {}
        for result in self.results:
            if result.endpoint_id not in endpoints:
                endpoints[result.endpoint_id] = []
            endpoints[result.endpoint_id].append(result.duration)
        return endpoints

    def _print_duration_statistics(self, identifier: str, durations: List[float]):
        """Affiche les statistiques de durée pour un identifiant donné."""
        self._write_to_output(f"\n{identifier}:")
        self._write_to_output(f"  Nombre: {len(durations)}")
        self._write_to_output(f"  Min: {min(durations):.4f}s")
        self._write_to_output(f"  Max: {max(durations):.4f}s")
        self._write_to_output(f"  Moyenne: {statistics.mean(durations):.4f}s")
        self._write_to_output(f"  Médiane: {statistics.median(durations):.4f}s")

    def _print_thread_statistics(self):
        """Calcule et affiche les statistiques par thread."""
        # Regrouper les durées par thread_id
        threads_data = self._group_results_by_thread()

        self._write_to_output("\n--- STATISTIQUES PAR THREAD ---")
        for thread_id, durations in threads_data.items():
            total_duration = sum(durations)
            self._write_to_output(
                f"Thread {thread_id}: {len(durations)} requètes en {total_duration:.4f}s"
            )

    def _group_results_by_thread(self) -> Dict[int, List[float]]:
        """Regroupe les résultats par thread_id."""
        threads = {}
        for result in self.results:
            if result.thread_id not in threads:
                threads[result.thread_id] = []
            threads[result.thread_id].append(result.duration)
        return threads

    def _print_http_status_statistics(self):
        """Affiche les statistiques des codes de statut HTTP."""
        self._write_to_output("\n--- STATISTIQUES DES CODES HTTP ---")

        # Classer les codes de statut par catégorie
        categories = self._categorize_status_codes()

        # Afficher chaque catégorie non vide
        self._print_status_categories(categories)

    def _categorize_status_codes(self) -> Dict[str, List[str]]:
        """Classe les codes de statut HTTP par catégorie."""
        categories = {
            "SuccÃ¨s (2xx)": [],
            "Redirection (3xx)": [],
            "Erreur client (4xx)": [],
            "Erreur serveur (5xx)": [],
            "Erreurs de connexion": [],
        }

        # Trier les codes de statut pour un affichage cohérent
        sorted_status_codes = sorted(self.status_code_counts.items())

        for code, count in sorted_status_codes:
            urls_dict = self.status_urls.get(code, {})
            if code < 0:
                categories["Erreurs de connexion"].append(
                    self._format_error_code(code, count, urls_dict)
                )
            elif 200 <= code < 300:
                categories["SuccÃ¨s (2xx)"].append(self._format_status_code(code, count))
            elif 300 <= code < 400:
                categories["Redirection (3xx)"].append(
                    self._format_status_code(code, count)
                )
            elif 400 <= code < 500:
                categories["Erreur client (4xx)"].append(
                    self._format_status_code(code, count, urls_dict)
                )
            elif 500 <= code < 600:
                categories["Erreur serveur (5xx)"].append(
                    self._format_status_code(code, count, urls_dict)
                )

        return categories

    def _format_error_code(
        self, code: int, count: int, urls_dict: Dict[str, int] = None
    ) -> str:
        """Formate un message pour un code d'erreur interne."""
        error_type = "Inconnue"
        if code == -1:
            error_type = "Erreur de requète HTTP"
        elif code == -2:
            error_type = "Exception générale"
        elif code == -3:
            error_type = "Fichier non trouvé"

        message = f"  {error_type} ({code}): {count} occurrences"

        # Ajouter les URLs en erreur si disponibles
        if urls_dict and len(urls_dict) > 0:
            message += "\n    URLs en erreur:"
            for url, url_count in urls_dict.items():
                message += f"\n    - {url} ({url_count} occurrences)"

        return message

    def _format_status_code(
        self, code: int, count: int, urls_dict: Dict[str, int] = None
    ) -> str:
        """Formate un message pour un code de statut HTTP."""
        message = f"  {code}: {count} occurrences"

        # Ajouter les URLs en erreur pour les codes d'erreur (4xx, 5xx)
        if urls_dict and len(urls_dict) > 0 and code >= 400:
            message += "\n    URLs en erreur:"
            for url, url_count in urls_dict.items():
                message += f"\n    - {url} ({url_count} occurrences)"

        return message

    def _print_status_categories(self, categories: Dict[str, List[str]]):
        """Affiche les catégories de codes de statut non vides."""
        for category, items in categories.items():
            if items:
                self._write_to_output(f"\n{category}:")
                for item in items:
                    self._write_to_output(item)

    async def _save_report_to_file(self):
        """Enregistre le rapport dans un fichier avec un timestamp."""
        try:
            # Créer le dossier des rapports s'il n'existe pas
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)

            # Générer un nom de fichier avec timestamp
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = reports_dir / f"test_report_{timestamp}.txt"

            # écrire le contenu du buffer dans le fichier de faÃ§on asynchrone
            async with aiofiles.open(report_filename, "w", encoding="utf-8") as f:
                await f.write(self.report_buffer.getvalue())

            self._write_to_output(
                f"\nRapport enregistré dans le fichier: {report_filename}"
            )
        except Exception as e:
            self._write_to_output(
                f"\nErreur lors de l'enregistrement du rapport: {str(e)}"
            )

    async def log_error_response(
        self,
        endpoint: str,
        thread_id: int,
        status_code: int,
        response_content: str,
        endpoint_id: str = None,
        url: str = None,
        exception_message: str = None,
    ):
        """Loggue le contenu d'une réponse en erreur dans un fichier séparé, avec l'URL et le type d'exception si disponible."""
        try:
            log_file = getattr(self, "error_log_file", None)
            if not log_file:
                logs_dir = Path("logs")
                logs_dir.mkdir(exist_ok=True)
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                log_file = logs_dir / f"errors_{timestamp}.log"
                self.error_log_file = log_file
            url_info = f"URL: {url}" if url else ""
            exc_info = (
                f"Type d'exception: {exception_message}" if exception_message else ""
            )
            async with aiofiles.open(log_file, "a", encoding="utf-8") as f:
                await f.write(
                    f"[Thread {thread_id}] [{endpoint_id or endpoint}] [HTTP {status_code}]\n{url_info}\n{exc_info}\n{response_content}\n{'-'*60}\n"
                )
        except Exception as e:
            print(f"Erreur lors de l'écriture du log d'erreur: {e}")

    async def print_summary(self, context_file=None, base_url=None):
        """Affiche un résumé des résultats du test et l'enregistre dans un fichier."""
        if not self.results:
            self._write_to_output("Aucun résultat enregistré")
            return

        # Réinitialiser le buffer de rapport
        self.report_buffer = io.StringIO()

        # Ajout : indiquer le fichier de contexte utilisé
        if context_file:
            self._write_to_output(f"Fichier de contexte utilisé : {context_file}")

        # Afficher les informations globales
        self._print_global_info()

        # Afficher les statistiques par endpoint
        self._print_endpoint_statistics()

        # Afficher les statistiques par thread
        self._print_thread_statistics()

        # Afficher les statistiques des codes HTTP
        self._print_http_status_statistics()

        # Générer le rapport Azure OpenAI si des appels ont été détectés
        if hasattr(self, "openai_monitor"):
            openai_report = await self.openai_monitor.generate_report()
            self._write_to_output(openai_report)

        # Ajout : afficher le lien vers le fichier d'erreur s'il existe et qu'il y a eu des erreurs
        if self.error_log_file and any(
            code >= 400 or code < 0 for code in self.status_code_counts
        ):
            self._write_to_output(
                f"\nFichier de log des erreurs : {self.error_log_file}"
            )

        # écrire le rapport dans un fichier
        await self._save_report_to_file()

        # Afficher le rapport à  la console
        print(self.report_buffer.getvalue())
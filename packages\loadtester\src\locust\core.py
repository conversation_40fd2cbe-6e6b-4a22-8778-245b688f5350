#!/usr/bin/env python3

import os
import sys
import argparse
import subprocess
from pathlib import Path


def setup_environment(args):
    """Setup environment variables for the test"""

    # Configuration
    if args.context:
        os.environ["CONFIG_CONTEXT"] = args.context

    if args.environment:
        os.environ["CONFIG_ENVIRONMENT"] = args.environment

    if args.test_type:
        os.environ["TEST_TYPE"] = args.test_type

    if args.skip_jwt:
        os.environ["SKIP_JWT_VALIDATION"] = "true"

    # Monitoring integrations
    if args.prometheus_url:
        os.environ["PROMETHEUS_PUSHGATEWAY_URL"] = args.prometheus_url

    if args.slack_webhook:
        os.environ["SLACK_WEBHOOK_URL"] = args.slack_webhook


def build_locust_command(args):
    """Build the locust command with appropriate arguments"""

    # Find the locustfile
    script_dir = Path(__file__).parent
    locustfile_path = script_dir / "locustfile.py"

    if not locustfile_path.exists():
        print(f"Error: Locustfile not found at {locustfile_path}")
        sys.exit(1)

    cmd = ["locust", "-f", str(locustfile_path)]

    # Add host if specified
    if args.host:
        cmd.extend(["--host", args.host])

    # Add user count and spawn rate
    if args.users:
        cmd.extend(["-u", str(args.users)])

    if args.spawn_rate:
        cmd.extend(["-r", str(args.spawn_rate)])

    # Add run time if specified (for headless mode)
    if args.run_time:
        cmd.extend(["-t", args.run_time])

    # Headless mode
    if args.headless:
        cmd.append("--headless")

    # Output files
    if args.csv:
        cmd.extend(["--csv", args.csv])

    if args.html:
        cmd.extend(["--html", args.html])

    # Log level
    if args.loglevel:
        cmd.extend(["--loglevel", args.loglevel])

    # Web UI port
    if args.web_port:
        cmd.extend(["--web-port", str(args.web_port)])

    # Master/worker mode for distributed testing
    if args.master:
        cmd.append("--master")
        if args.master_bind_host:
            cmd.extend(["--master-bind-host", args.master_bind_host])
        if args.master_bind_port:
            cmd.extend(["--master-bind-port", str(args.master_bind_port)])
        if args.expect_workers:
            cmd.extend(["--expect-workers", str(args.expect_workers)])

    if args.worker:
        cmd.append("--worker")
        if args.master_host:
            cmd.extend(["--master-host", args.master_host])
        if args.master_port:
            cmd.extend(["--master-port", str(args.master_port)])

    return cmd


def main():
    """Main launcher function"""

    parser = argparse.ArgumentParser(
        description="Launch Test.Charge load tests with Locust",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic test with andoc context
  %(prog)s --context andoc --host https://api.example.com

  # Stress test with 100 users, headless mode
  %(prog)s --context andoc --test-type stress -u 100 -r 10 --headless --run-time 5m

  # Smoke test with monitoring
  %(prog)s --context andoc --test-type smoke --prometheus-url http://localhost:9091

  # Distributed testing (master node)
  %(prog)s --context andoc --master --expect-workers 3

  # Distributed testing (worker node)
  %(prog)s --worker --master-host *************

Test Types:
  standard  - Balanced mix of user types (default)
  stress    - Heavy load with burst users
  smoke     - Lightweight smoke tests
  chaos     - Random and failure testing
        """,
    )

    # Test.Charge specific options
    tc_group = parser.add_argument_group("Test.Charge Configuration")
    tc_group.add_argument(
        "--context", "-c", help="Configuration context (e.g., andoc, talperftraitement)"
    )
    tc_group.add_argument(
        "--environment",
        "-e",
        help="Environment configuration (e.g., dev, staging, prod)",
    )
    tc_group.add_argument(
        "--test-type",
        choices=["standard", "stress", "smoke", "chaos"],
        default="standard",
        help="Type of test to run",
    )
    tc_group.add_argument(
        "--skip-jwt", action="store_true", help="Skip JWT token validation"
    )

    # Locust options
    locust_group = parser.add_argument_group("Locust Configuration")
    locust_group.add_argument(
        "--host", "-H", help="Host to test (e.g., https://api.example.com)"
    )
    locust_group.add_argument(
        "--users", "-u", type=int, help="Number of concurrent users"
    )
    locust_group.add_argument(
        "--spawn-rate", "-r", type=float, help="Rate to spawn users (per second)"
    )
    locust_group.add_argument(
        "--run-time", "-t", help="Test run time (e.g., 1m, 30s, 1h)"
    )
    locust_group.add_argument(
        "--headless", action="store_true", help="Run in headless mode (no web UI)"
    )
    locust_group.add_argument(
        "--web-port", type=int, default=8089, help="Port for web UI (default: 8089)"
    )
    locust_group.add_argument(
        "--loglevel",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Log level",
    )

    # Output options
    output_group = parser.add_argument_group("Output Options")
    output_group.add_argument("--csv", help="Store request statistics to CSV files")
    output_group.add_argument("--html", help="Store HTML report file path")

    # Distributed testing
    dist_group = parser.add_argument_group("Distributed Testing")
    dist_group.add_argument("--master", action="store_true", help="Run as master node")
    dist_group.add_argument("--worker", action="store_true", help="Run as worker node")
    dist_group.add_argument(
        "--master-host",
        default="127.0.0.1",
        help="Master node hostname/IP (for workers)",
    )
    dist_group.add_argument(
        "--master-port", type=int, default=5557, help="Master node port (for workers)"
    )
    dist_group.add_argument(
        "--master-bind-host", default="*", help="Interface to bind master to"
    )
    dist_group.add_argument(
        "--master-bind-port", type=int, default=5557, help="Port to bind master to"
    )
    dist_group.add_argument(
        "--expect-workers", type=int, help="Number of workers to expect (master mode)"
    )

    # Monitoring integrations
    monitor_group = parser.add_argument_group("Monitoring Integration")
    monitor_group.add_argument("--prometheus-url", help="Prometheus Pushgateway URL")
    monitor_group.add_argument("--slack-webhook", help="Slack webhook URL for alerts")

    args = parser.parse_args()

    # Validation
    if args.master and args.worker:
        parser.error("Cannot run as both master and worker")

    if not args.context and not args.worker:
        parser.error("Context is required (unless running as worker)")

    # Setup environment
    setup_environment(args)

    # Build and run command
    cmd = build_locust_command(args)

    print("🚀 Starting Test.Charge load test with Locust")
    print(f"Context: {args.context or 'N/A'}")
    print(f"Test Type: {args.test_type}")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 50)

    try:
        # Run locust
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Locust execution failed with exit code {e.returncode}")
        sys.exit(e.returncode)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(0)
    except FileNotFoundError:
        print("❌ Locust not found. Please install locust: pip install locust")
        sys.exit(1)


if __name__ == "__main__":
    main()

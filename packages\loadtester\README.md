# Application de Test de Charge HTTP avec Intégration Locust

Cette application permet de tester les performances d'une API en ligne avec deux modes de fonctionnement :
- **Mode Asyncio** : Mode original avec exécution parallÃ¨le via asyncio
- **Mode Locust** : Intégration avancée avec Locust pour des tests de charge d'entreprise

## ðŸš€ Démarrage Rapide

### Installation

```bash
# Installation complÃ¨te avec support Locust
pip install -e ".[all]"

# Installation basique (mode asyncio seulement)
pip install -e .
```

### Utilisation Rapide

```bash
# Mode asyncio original (inchangé)
python -m src.main -c configs/andoc.json --mode asyncio

# Mode asyncio enhanced - test de validation
python -m src.main -c configs/andoc.json --mode asyncio --test-type smoke

# Mode asyncio enhanced - test de charge standard
python -m src.main -c configs/andoc.json --mode asyncio --test-type standard --asyncio-users 20 --asyncio-duration 300

# Mode asyncio enhanced - test de stress
python -m src.main -c configs/andoc.json --mode asyncio --test-type stress --asyncio-users 50 --asyncio-duration 600 --ramp-up-time 60

# Mode Locust avec interface web
python -m src.main -c configs/andoc.json --mode locust --locust-ui

# Mode Locust test de stress
python -m src.main -c configs/andoc.json --mode locust --locust-test-type stress --users 100 --headless --run-time 5m
```

### PowerShell (Windows)

```powershell
# Mode asyncio original
.\test-load-context.ps1 -context andoc

# Mode asyncio enhanced
python -m src.main -c configs/andoc.json --mode asyncio --test-type stress --asyncio-users 30 --asyncio-duration 300

# Mode Locust
.\test-load-context.ps1 -context andoc -mode locust -testType stress -users 100 -headless
```

## Fonctionnalités

### Mode Asyncio

#### Original (Mode Classique)
- Authentification via JWT dans l'en-tète `X-CGPT-AUTHORIZATION`
- Validation automatique du jeton JWT au démarrage
- Exécution parallÃ¨le avec un nombre configurable de threads
- Séquences d'appels HTTP avec possibilité de chaÃ®nage
- Chronométrage détaillé et rapport des erreurs HTTP
- Téléchargement de fichiers via form-data
- Rapports de statistiques détaillés

#### Enhanced (Mode Avancé) âœ¨ **NOUVEAU**
- **4 types de test** : standard, stress, smoke, chaos
- **Configuration avancée** : concurrence, montée en charge, think time
- **Monitoring temps réel** : statistiques live pendant l'exécution
- **Rapports détaillés** : CSV et JSON avec métriques complÃ¨tes
- **Patterns de charge intelligents** : comportements différenciés par type de test
- **Gestion d'erreurs avancée** : seuils configurables et injection de chaos
- **Compatible** : utilise les mèmes fichiers de configuration
- **ðŸ† Défi 2000 utilisateurs** : Scripts complets pour tester jusqu'Ã  2000 utilisateurs concurrents sur machine locale

### Mode Locust (Avancé)
- **Tests de charge d'entreprise** avec interface web intuitive
- **Multiples types d'utilisateurs** : Standard, Stress, Smoke, Chaos, etc.
- **Tests distribués** : Support master/worker pour haute charge
- **Monitoring avancé** : Intégration Prometheus, InfluxDB, Slack
- **Rapports interactifs** : HTML avec graphiques, CSV, JSON
- **Scénarios complexes** : Tests de montée en charge, pics de trafic
- **Configuration flexible** : Support environnements multiples
- **Toute la fonctionnalité asyncio** préservée et intégrée

## ðŸŽ¯ Quel Mode Choisir ?

| Scénario | Mode Recommandé | Pourquoi |
|----------|-----------------|----------|
| Tests simples, débogage | **Asyncio Original** | Plus simple, rapideà  configurer |
| Validation rapide, smoke tests | **Asyncio Enhanced** | Types de test intégrés, monitoring avancé |
| Tests de charge < 50 utilisateurs | **Asyncio Enhanced** | Performance adéquate avec fonctionnalités avancées |
| Tests de charge 50-100 utilisateurs | **Asyncio Enhanced** ou **Locust** | Enhanced pour simplicité, Locust pour évolutivité |
| Tests de charge > 100 utilisateurs | **Locust** | Meilleure scalabilité et support distribué |
| Tests de stress complexes | **Locust** | Types d'utilisateurs spécialisés et patterns avancés |
| Interface graphique souhaitée | **Locust** | Interface web interactive |
| Tests distribués | **Locust** | Support natif master/worker |
| CI/CD avec monitoring | **Asyncio Enhanced** ou **Locust** | Enhanced pour simplicité, Locust pour complexité |

## Installation

```bash
pip install -e .
```

## Utilisation

### 1. Préparer les fichiers de configuration

- Placez vos fichiers de contexte dans le dossier `configs/` (ex: `andoc.json`, `talperftraitement.json`, etc.)
- Placez la configuration globale dans `config.global.json`à  la racine du projet

### 2. Lancer les tests avec le script PowerShell

Utilisez le script `test-load-context.ps1` pour choisir le mode et lancer les tests :

```powershell
# Mode original (asyncio)
./test-load-context.ps1 -context andoc

# Mode Locust avec interface web
./test-load-context.ps1 -context andoc -mode locust -locustUI

# Test de stress avec Locust
./test-load-context.ps1 -context andoc -mode locust -testType stress -users 100 -headless -runTime "5m"
```

### 3. Lancer directement avec Python

```bash
# Mode original (asyncio)
python -m src.main -c andoc

# Mode Locust avec interface web
python -m src.main -c andoc --mode locust --locust-ui

# Test de stress Locust
python -m src.main -c andoc --mode locust --test-type stress --users 100 --headless --run-time 5m
```

## Validation du jeton JWT

Au démarrage, l'application vérifie automatiquement:
- La structure du jeton JWT
- La date d'expiration du jeton
- Si possible, fait un appel léger au serveur pour confirmer que le jeton est accepté

Si le jeton est invalide ou expiré, l'application affiche un message d'erreur et s'arrète.
Pour désactiver cette validation, utilisez l'option `--skip-jwt-validation`.

## Configuration

### Configuration de Base
- `base_url`: URL de base de l'API
- `jwt_token`: Jeton JWT pour l'authentification
- `num_threads` (asyncio) / `num_users` (locust): Nombre d'utilisateurs parallÃ¨les
- `verify_ssl`: Vérification des certificats SSL
- `endpoints`: Liste des points d'accÃ¨sà  tester

### Configuration des Endpoints
Pour chaque endpoint:
- `url`: Chemin relatif de l'endpoint
- `method`: Méthode HTTP (GET, POST, PUT, DELETE)
- `data`: Donnéesà  envoyer
- `extract`: Valeursà  extraire de la réponse
- `depends_on`: Dépendances vers d'autres endpoints
- `id`: Identifiant personnalisé

## Dépendances entre requètes

Utilisez `depends_on` et `extract` pour créer des séquences de requètes:

```json
{
  "url": "/login",
  "method": "POST",
  "extract": [{"key": "token", "valueFrom": "access_token"}],
  "id": "authentication"
},
{
  "url": "/protected-resource/{token}",
  "method": "GET",
  "depends_on": "authentication"
}
```

## Rapport de statistiques

L'application génÃ¨re des rapports détaillés:
- **Mode Asyncio**: Rapport console avec statistiques par endpoint et thread
- **Mode Locust**: Rapports HTML interactifs, CSV, JSON avec graphiques

## ðŸ“Š Monitoring et Alertes

Le mode Locust supporte l'intégration avec :
- **Prometheus** : Métriques temps réel
- **InfluxDB** : Stockage des séries temporelles
- **Slack** : Alertes en temps réel
- **Webhooks** : Intégrations personnalisées

Exemple avec monitoring :
```bash
PROMETHEUS_PUSHGATEWAY_URL=http://localhost:9091 \
SLACK_WEBHOOK_URL=https://hooks.slack.com/... \
python -m src.main -c andoc --mode locust --users 100 --headless
```

## ðŸ† Défi 2000 Utilisateurs - Asyncio Enhanced

**Nouveau !** Testez les limites de votre machine locale avec le défi 2000 utilisateurs :

```bash
# Préparation automatique du systÃ¨me
./scripts/setup-challenge.sh

# Optimisation systÃ¨me (recommandé)
sudo ./scripts/optimize-system.sh

# Lancement du défi complet
./scripts/asyncio-2000-user-challenge.sh

# Monitoring en temps réel (terminal séparé)
./scripts/live-dashboard.sh

# Analyse des résultats
python3 scripts/analyze-results.py
```

**Fonctionnalités du défi :**
- ðŸ“Š **Tests progressifs** : de 10à  2000 utilisateurs par paliers
- ðŸ“Š **Monitoring complet** : CPU, mémoire, connexions, threads, fichiers ouverts
- ðŸ“‹ **Rapports détaillés** : Graphiques, chronologies, analyse des goulots d'étranglement
- âš™ï¸ **Optimisation systÃ¨me** : Configuration automatique pour haute concurrence
- ðŸ“Š **Dashboard temps réel** : Surveillance live des performances
- ðŸ›¡ï¸ **Sécurité** : Arrèt automatique en cas de surcharge systÃ¨me

> ðŸ† **Objectif** : Découvrir la limite réelle de votre machine et optimiser les performances pour des tests haute concurrence

Consultez le **[Guide complet du défi 2000 utilisateurs](docs/2000-user-challenge-guide.md)** pour tous les détails !

## Documentation Complete

- **[2000-User Challenge Guide](docs/2000-user-challenge-guide.md)** - Guide complet pour tester jusqu'Ã  2000 utilisateurs concurrents ðŸ†
- **[Enhanced Asyncio Framework](docs/enhanced-asyncio-framework.md)** - Documentation détaillée du mode asyncio avancé âœ¨
- **[Asyncio Scalability Guide](docs/asyncio-scalability-guide.md)** - Optimisations systÃ¨me pour haute concurrence ðŸ”§
- **[Guide d'Intégration Locust](docs/locust_integration.md)** - Documentation complÃ¨te du mode Locust
- **[Exemples de Configuration](configs/example_locust_integration.json)** - Configuration exemple avec toutes les options
- **[Scripts de Démonstration](scripts/)** - Scripts bash et PowerShell pour tester les fonctionnalités

## Support et Contribution

Pour signaler des bugs, demander des fonctionnalités ou contribuer :
1. Vérifiez les issues existantes
2. Créez une nouvelle issue avec le template approprié
3. Pour les contributions, suivez le guide dans CONTRIBUTING.md

## Security

âš ï¸ **Attention**: Désactiver la vérification SSL (`verify_ssl: false`) peut exposer votre applicationà  des risques de sécurité comme des attaques man-in-the-middle. Utilisez cette option uniquement dans des environnements de test contrÃ´lés ou pour des API internes avec des certificats auto-signés.
{"test_duration": 12.013394355773926, "total_requests": 18, "successful_requests": 17, "failed_requests": 1, "success_rate": 94.44444444444444, "response_times": {"average": 0.25954727331797284, "median": 0.26608800888061523, "min": 0, "max": 0.32433319091796875, "std_dev": 0.06799926457049987, "percentiles": {"p50": 0.26608800888061523, "p75": 0.27524417638778687, "p90": 0.30894248485565184, "p95": 0.3237600803375244, "p99": 0.3242185688018799, "p99.9": 0.3243217287063599}}, "throughput": {"average_rps": 1.4983179676507676, "current_rps": 1.9678853298020382}, "error_analysis": {"total_errors": 1, "error_rate": 5.555555555555555, "status_code_breakdown": {"502": 1}, "endpoint_error_breakdown": {"/api/items/{item_id}": 1}, "common_error_messages": {"Dependency preparation failed": 1}}, "endpoint_breakdown": {"GET /health": {"total_requests": 4, "success_rate": 100.0, "average_response_time": 0.2815089225769043, "percentiles": {"p50": 0.26801836490631104, "p90": 0.3070461988449097, "p95": 0.31535257101058956, "p99": 0.3219976687431335}, "error_breakdown": {}}, "POST /auth/login": {"total_requests": 4, "success_rate": 100.0, "average_response_time": 0.29586881399154663, "percentiles": {"p50": 0.29078876972198486, "p90": 0.31782386302947996, "p95": 0.32107852697372435, "p99": 0.3236822581291199}, "error_breakdown": {}}, "GET /auth/validate": {"total_requests": 4, "success_rate": 100.0, "average_response_time": 0.2629457712173462, "percentiles": {"p50": 0.26439499855041504, "p90": 0.2667355537414551, "p95": 0.2669283151626587, "p99": 0.2670825242996216}, "error_breakdown": {}}, "GET /api/user/profile": {"total_requests": 4, "success_rate": 100.0, "average_response_time": 0.2621745467185974, "percentiles": {"p50": 0.2628253698348999, "p90": 0.2647931337356567, "p95": 0.2650701880455017, "p99": 0.2652918314933777}, "error_breakdown": {}}, "POST /api/items": {"total_requests": 1, "success_rate": 100.0, "average_response_time": 0.2618587017059326, "percentiles": {"p50": 0.2618587017059326, "p90": 0.2618587017059326, "p95": 0.2618587017059326, "p99": 0.2618587017059326}, "error_breakdown": {}}, "GET /api/items/{item_id}": {"total_requests": 1, "success_rate": 0.0, "average_response_time": 0.0, "percentiles": {"p50": 0, "p90": 0, "p95": 0, "p99": 0}, "error_breakdown": {"502": 1}}}, "performance_score": {"score": 90.7, "rating": "EXCELLENT", "components": {"success_rate": 94.4, "response_time": 100, "throughput": 64.8}}, "alerts_summary": {"total_alerts": 0}, "real_time_metrics": {"current_throughput": 1.9678853298020382, "current_error_rate": 5.555555555555555, "current_avg_response_time": 0.25954727331797284, "active_requests": 9, "total_requests": 18, "uptime_seconds": 9.14687442779541}, "test_metadata": {"start_time": 1756808696.054491, "end_time": 1756808708.0638745, "duration": 12.0093834400177, "total_workers": 3, "worker_details": {"0": {"start_time": 1756808696.0545793, "requests": 0, "errors": 0, "end_time": 1756808697.4935637}, "1": {"start_time": 1756808696.105483, "requests": 0, "errors": 0, "end_time": 1756808708.0638268}, "2": {"start_time": 1756808696.1055002, "requests": 0, "errors": 0, "end_time": 1756808707.9528248}}}}
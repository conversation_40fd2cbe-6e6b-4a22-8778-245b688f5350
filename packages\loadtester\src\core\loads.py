"""
Test Types for Enhanced Asyncio Framework
========================================

Defines different test type classes that modify execution behavior,
similar to Locust's user classes but adapted for asyncio sequences.
"""

import random
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from dataclasses import dataclass


@dataclass
class TestParameters:
    """Parameters that control test execution"""
    concurrency: int = 10          # Number of concurrent workers
    duration: Optional[float] = None  # Test duration in seconds
    iterations: Optional[int] = None  # Number of iterations per worker
    ramp_up_time: float = 0.0      # Time to gradually increase load
    cool_down_time: float = 0.0    # Time to gradually decrease load
    think_time_min: float = 1.0    # Minimum wait between requests
    think_time_max: float = 3.0    # Maximum wait between requests
    error_threshold: float = 0.1   # Stop if error rate exceeds this
    chaos_factor: float = 0.0      # Amount of randomness (0.0-1.0)


@dataclass 
class WorkerConfig:
    """Configuration for a single worker"""
    worker_id: int
    delay_start: float = 0.0       # Delay before this worker starts
    think_time_min: float = 1.0
    think_time_max: float = 3.0
    sequence_modifier: Optional[str] = None  # "full", "partial", "random"
    error_injection: float = 0.0   # Probability of injecting errors


class TestType(ABC):
    """Base class for different test types"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.name = self.__class__.__name__.replace('Test', '').lower()
        
    @abstractmethod
    def get_test_parameters(self, user_specified_params: Dict[str, Any] = None) -> TestParameters:
        """Get test parameters for this test type"""
        pass
        
    @abstractmethod
    def generate_worker_configs(self, params: TestParameters) -> List[WorkerConfig]:
        """Generate configurations for individual workers"""
        pass
        
    @abstractmethod
    def should_continue_test(self, elapsed_time: float, iteration: int, 
                           error_rate: float) -> bool:
        """Determine if the test should continue"""
        pass
        
    def modify_sequence_for_worker(self, endpoints: List[Dict[str, Any]], 
                                  worker_config: WorkerConfig) -> List[Dict[str, Any]]:
        """Modify the endpoint sequence for a specific worker"""
        if worker_config.sequence_modifier == "partial":
            # Use only first half of endpoints for lighter load
            return endpoints[:len(endpoints)//2]
        elif worker_config.sequence_modifier == "random":
            # Random subset of endpoints, but ensure dependencies are preserved
            # Build dependency graph to include required endpoints
            required_endpoints = set()
            endpoint_by_id = {ep.get("id"): ep for ep in endpoints if ep.get("id")}
            
            # Start with a random selection
            count = random.randint(1, len(endpoints))
            selected_indices = set(random.sample(range(len(endpoints)), count))
            
            # For each selected endpoint, ensure its dependencies are included
            for idx in list(selected_indices):
                endpoint = endpoints[idx]
                if "depends_on" in endpoint and endpoint["depends_on"]:
                    # Find and add the dependency
                    dep_id = endpoint["depends_on"]
                    for dep_idx, dep_ep in enumerate(endpoints):
                        if dep_ep.get("id") == dep_id:
                            selected_indices.add(dep_idx)
                            # Recursively add dependencies of dependencies
                            if "depends_on" in dep_ep and dep_ep["depends_on"]:
                                for dep2_idx, dep2_ep in enumerate(endpoints):
                                    if dep2_ep.get("id") == dep_ep["depends_on"]:
                                        selected_indices.add(dep2_idx)
            
            # Return endpoints in original order
            return [endpoints[i] for i in sorted(selected_indices)]
        else:
            # Full sequence
            return endpoints
            
    def inject_chaos(self, worker_config: WorkerConfig) -> bool:
        """Decide if this request should have chaos injected"""
        return random.random() < worker_config.error_injection


class StandardTest(TestType):
    """Standard balanced load testing"""
    
    def get_test_parameters(self, user_specified_params: Dict[str, Any] = None) -> TestParameters:
        params = TestParameters(
            concurrency=10,
            duration=300.0,  # 5 minutes
            ramp_up_time=30.0,
            cool_down_time=30.0,
            think_time_min=1.0,
            think_time_max=3.0,
            error_threshold=0.1,
            chaos_factor=0.0
        )
        
        if user_specified_params:
            # Override with user-specified parameters
            for key, value in user_specified_params.items():
                if hasattr(params, key):
                    setattr(params, key, value)
                    
        return params
        
    def generate_worker_configs(self, params: TestParameters) -> List[WorkerConfig]:
        configs = []
        
        # Stagger worker starts during ramp-up
        start_delay_increment = params.ramp_up_time / params.concurrency if params.ramp_up_time > 0 else 0
        
        for i in range(params.concurrency):
            config = WorkerConfig(
                worker_id=i,
                delay_start=i * start_delay_increment,
                think_time_min=params.think_time_min,
                think_time_max=params.think_time_max,
                sequence_modifier="full",
                error_injection=0.0
            )
            configs.append(config)
            
        return configs
        
    def should_continue_test(self, elapsed_time: float, iteration: int, 
                           error_rate: float) -> bool:
        # Continue until duration is reached or error threshold exceeded
        return error_rate <= 0.1  # Allow up to 10% error rate


class StressTest(TestType):
    """High-intensity stress testing"""
    
    def get_test_parameters(self, user_specified_params: Dict[str, Any] = None) -> TestParameters:
        params = TestParameters(
            concurrency=50,  # Higher concurrency
            duration=600.0,  # 10 minutes
            ramp_up_time=60.0,  # Longer ramp-up
            cool_down_time=60.0,
            think_time_min=0.1,  # Minimal wait times
            think_time_max=1.0,
            error_threshold=0.2,  # Allow higher error rate
            chaos_factor=0.1      # Some randomness
        )
        
        if user_specified_params:
            for key, value in user_specified_params.items():
                if hasattr(params, key):
                    setattr(params, key, value)
                    
        return params
        
    def generate_worker_configs(self, params: TestParameters) -> List[WorkerConfig]:
        configs = []
        
        start_delay_increment = params.ramp_up_time / params.concurrency if params.ramp_up_time > 0 else 0
        
        for i in range(params.concurrency):
            # Create different worker types for stress testing
            if i % 4 == 0:
                # Aggressive workers (25%)
                config = WorkerConfig(
                    worker_id=i,
                    delay_start=i * start_delay_increment * 0.5,  # Start earlier
                    think_time_min=0.05,
                    think_time_max=0.5,
                    sequence_modifier="full",
                    error_injection=0.02
                )
            elif i % 4 == 1:
                # Burst workers (25%)
                config = WorkerConfig(
                    worker_id=i,
                    delay_start=i * start_delay_increment,
                    think_time_min=0.1,
                    think_time_max=2.0,
                    sequence_modifier="random",
                    error_injection=0.01
                )
            elif i % 4 == 2:
                # Steady workers (25%)
                config = WorkerConfig(
                    worker_id=i,
                    delay_start=i * start_delay_increment,
                    think_time_min=0.5,
                    think_time_max=1.5,
                    sequence_modifier="full",
                    error_injection=0.01
                )
            else:
                # Heavy load workers (25%)
                config = WorkerConfig(
                    worker_id=i,
                    delay_start=i * start_delay_increment * 0.8,
                    think_time_min=0.1,
                    think_time_max=0.8,
                    sequence_modifier="full",
                    error_injection=0.03
                )
                
            configs.append(config)
            
        return configs
        
    def should_continue_test(self, elapsed_time: float, iteration: int, 
                           error_rate: float) -> bool:
        # Allow higher error rates for stress testing
        return error_rate <= 0.25  # Up to 25% error rate acceptable


class SmokeTest(TestType):
    """Light validation testing"""
    
    def get_test_parameters(self, user_specified_params: Dict[str, Any] = None) -> TestParameters:
        params = TestParameters(
            concurrency=3,     # Very low concurrency
            duration=120.0,    # 2 minutes
            ramp_up_time=10.0,
            cool_down_time=10.0,
            think_time_min=2.0,  # Longer waits
            think_time_max=5.0,
            error_threshold=0.05,  # Low error tolerance
            chaos_factor=0.0
        )
        
        if user_specified_params:
            for key, value in user_specified_params.items():
                if hasattr(params, key):
                    setattr(params, key, value)
                    
        return params
        
    def generate_worker_configs(self, params: TestParameters) -> List[WorkerConfig]:
        configs = []
        
        start_delay_increment = params.ramp_up_time / params.concurrency if params.ramp_up_time > 0 else 0
        
        for i in range(params.concurrency):
            # First worker does full sequence, others do partial
            sequence_modifier = "full" if i == 0 else "partial"
            
            config = WorkerConfig(
                worker_id=i,
                delay_start=i * start_delay_increment,
                think_time_min=params.think_time_min,
                think_time_max=params.think_time_max,
                sequence_modifier=sequence_modifier,
                error_injection=0.0  # No error injection for smoke tests
            )
            configs.append(config)
            
        return configs
        
    def should_continue_test(self, elapsed_time: float, iteration: int, 
                           error_rate: float) -> bool:
        # Very low error tolerance for smoke tests
        return error_rate <= 0.05  # Only 5% error rate allowed


class ChaosTest(TestType):
    """Unpredictable load patterns"""
    
    def get_test_parameters(self, user_specified_params: Dict[str, Any] = None) -> TestParameters:
        params = TestParameters(
            concurrency=random.randint(15, 35),  # Random concurrency
            duration=900.0,    # 15 minutes
            ramp_up_time=random.uniform(30, 120),  # Random ramp-up
            cool_down_time=random.uniform(30, 90),
            think_time_min=0.1,
            think_time_max=10.0,  # Wide range
            error_threshold=0.3,   # High tolerance for chaos
            chaos_factor=0.3       # High chaos factor
        )
        
        if user_specified_params:
            for key, value in user_specified_params.items():
                if hasattr(params, key):
                    setattr(params, key, value)
                    
        return params
        
    def generate_worker_configs(self, params: TestParameters) -> List[WorkerConfig]:
        configs = []
        
        for i in range(params.concurrency):
            # Completely randomized worker behavior
            config = WorkerConfig(
                worker_id=i,
                delay_start=random.uniform(0, params.ramp_up_time),
                think_time_min=random.uniform(0.1, 2.0),
                think_time_max=random.uniform(3.0, 10.0),
                sequence_modifier=random.choice(["full", "partial", "random"]),
                error_injection=random.uniform(0.01, 0.1)  # Random error injection
            )
            configs.append(config)
            
        return configs
        
    def should_continue_test(self, elapsed_time: float, iteration: int, 
                           error_rate: float) -> bool:
        # Random decision making with bias towards continuing
        if error_rate > 0.5:  # Too many errors
            return False
            
        # Random chance to stop early or extend test
        random_factor = random.random()
        if random_factor < 0.05:  # 5% chance to stop early
            return False
        elif random_factor > 0.95:  # 5% chance to extend
            return True
            
        return error_rate <= 0.3


# Registry of available test types
TEST_TYPES = {
    "standard": StandardTest,
    "stress": StressTest, 
    "smoke": SmokeTest,
    "chaos": ChaosTest
}


def create_test_type(test_type: str, config: Dict[str, Any]) -> TestType:
    """Factory function to create test type instances"""
    if test_type not in TEST_TYPES:
        raise ValueError(f"Unknown test type: {test_type}. Available types: {list(TEST_TYPES.keys())}")
        
    return TEST_TYPES[test_type](config)
Fichier de contexte utilisé : configs/localapi_simple.json

===== RéSUMé DU TEST =====
Temps d'exécution total: 2.8506s
Nombre total de requètes: 45
Nombre de threads: 5
Nombre de tÃ¢ches par thread: 9
Nombre total de tÃ¢ches: 45

--- STATISTIQUES PAR ENDPOINT ---

health:
  Nombre: 5
  Min: 0.2611s
  Max: 0.3109s
  Moyenne: 0.2728s
  Médiane: 0.2637s

login:
  Nombre: 5
  Min: 0.3006s
  Max: 0.3312s
  Moyenne: 0.3085s
  Médiane: 0.3027s

validate:
  Nombre: 5
  Min: 0.2676s
  Max: 0.2691s
  Moyenne: 0.2683s
  Médiane: 0.2684s

profile:
  Nombre: 5
  Min: 0.2630s
  Max: 0.2656s
  Moyenne: 0.2649s
  Médiane: 0.2654s

create_item:
  Nombre: 5
  Min: 0.2934s
  Max: 0.3421s
  Moyenne: 0.3130s
  Médiane: 0.3119s

get_item:
  Nombre: 5
  Min: 0.2627s
  Max: 0.2668s
  Moyenne: 0.2647s
  Médiane: 0.2647s

list_items:
  Nombre: 5
  Min: 0.2921s
  Max: 0.3100s
  Moyenne: 0.2996s
  Médiane: 0.2936s

search:
  Nombre: 5
  Min: 0.4924s
  Max: 0.5564s
  Moyenne: 0.5209s
  Médiane: 0.5229s

analytics:
  Nombre: 5
  Min: 0.2536s
  Max: 0.2667s
  Moyenne: 0.2636s
  Médiane: 0.2657s

--- STATISTIQUES PAR THREAD ---
Thread 4: 9 requètes en 2.7725s
Thread 1: 9 requètes en 2.7735s
Thread 3: 9 requètes en 2.8042s
Thread 2: 9 requètes en 2.7443s
Thread 0: 9 requètes en 2.7870s

--- STATISTIQUES DES CODES HTTP ---

SuccÃ¨s (2xx):
  200: 45 occurrences

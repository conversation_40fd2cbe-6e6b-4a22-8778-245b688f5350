{"test_duration": 6.722638845443726, "total_requests": 4, "successful_requests": 2, "failed_requests": 2, "success_rate": 50.0, "response_times": {"average": 0.2846938371658325, "median": 0.28468430042266846, "min": 0.2585883140563965, "max": 0.3108184337615967, "std_dev": 0.030137323931746566, "percentiles": {"p50": 0.28468430042266846, "p75": 0.3107810616493225, "p90": 0.310803484916687, "p95": 0.3108109593391418, "p99": 0.3108169388771057, "p99.9": 0.31081828427314756}}, "throughput": {"average_rps": 0.5949965115289774, "current_rps": 0}, "error_analysis": {"total_errors": 2, "error_rate": 50.0, "status_code_breakdown": {"400": 2}, "endpoint_error_breakdown": {"/health": 2}, "common_error_messages": {"EndpointStats.__init__() missing 1 required positional argument: 'endpoint'": 2}}, "endpoint_breakdown": {}, "performance_score": {"score": 60.0, "rating": "POOR", "components": {"success_rate": 50.0, "response_time": 100, "throughput": 0}}, "alerts_summary": {"total_alerts": 0}, "real_time_metrics": {}, "test_metadata": {"start_time": **********.2316823, "end_time": **********.950767, "duration": 6.719084739685059, "total_workers": 3, "worker_details": {"0": {"start_time": **********.2317564, "requests": 0, "errors": 0, "end_time": **********.5426834}, "1": {"start_time": **********.2773986, "requests": 0, "errors": 0, "end_time": **********.8811262}, "2": {"start_time": **********.2774112, "requests": 0, "errors": 0, "end_time": **********.9507315}}}}
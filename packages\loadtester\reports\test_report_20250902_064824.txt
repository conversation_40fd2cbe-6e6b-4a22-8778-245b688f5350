Fichier de contexte utilisé : configs/localapi_simple.json

===== RéSUMé DU TEST =====
Temps d'exécution total: 2.7860s
Nombre total de requètes: 45
Nombre de threads: 5
Nombre de tÃ¢ches par thread: 9
Nombre total de tÃ¢ches: 45

--- STATISTIQUES PAR ENDPOINT ---

health:
  Nombre: 5
  Min: 0.2633s
  Max: 0.3091s
  Moyenne: 0.2754s
  Médiane: 0.2680s

login:
  Nombre: 5
  Min: 0.2950s
  Max: 0.3247s
  Moyenne: 0.3053s
  Médiane: 0.3006s

validate:
  Nombre: 5
  Min: 0.2655s
  Max: 0.2732s
  Moyenne: 0.2697s
  Médiane: 0.2718s

profile:
  Nombre: 5
  Min: 0.2642s
  Max: 0.2793s
  Moyenne: 0.2754s
  Médiane: 0.2784s

create_item:
  Nombre: 5
  Min: 0.3124s
  Max: 0.3548s
  Moyenne: 0.3311s
  Médiane: 0.3273s

get_item:
  Nombre: 5
  Min: 0.2618s
  Max: 0.2722s
  Moyenne: 0.2661s
  Médiane: 0.2648s

list_items:
  Nombre: 5
  Min: 0.2968s
  Max: 0.3140s
  Moyenne: 0.3060s
  Médiane: 0.3088s

search:
  Nombre: 5
  Min: 0.3801s
  Max: 0.4665s
  Moyenne: 0.4140s
  Médiane: 0.4088s

analytics:
  Nombre: 5
  Min: 0.2590s
  Max: 0.2757s
  Moyenne: 0.2673s
  Médiane: 0.2662s

--- STATISTIQUES PAR THREAD ---
Thread 4: 9 requètes en 2.7408s
Thread 1: 9 requètes en 2.7128s
Thread 0: 9 requètes en 2.7300s
Thread 3: 9 requètes en 2.6838s
Thread 2: 9 requètes en 2.6851s

--- STATISTIQUES DES CODES HTTP ---

SuccÃ¨s (2xx):
  200: 45 occurrences

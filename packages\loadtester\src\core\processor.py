import re
import os
import traceback
import asyncio
import json
import aiofiles
from typing import Dict, Any, Optional, List, Tuple, Union

from .clients import HttpClient
from .timer import Timer
from .utils import get_config_paths, validate_jwt

class TestSequence:
    """Exécute une séquence de tests HTTP."""

    def __init__(self, config: Dict[str, Any], timer: Timer):
        self.config = config
        self.timer = timer
        # Map pour retrouver les IDs des endpoints par URL
        self.url_to_id_map = {}

        # Construire la map URL -> ID pour une recherche facile
        for endpoint in config.get("endpoints", []):
            if "id" in endpoint and "url" in endpoint:
                self.url_to_id_map[endpoint["url"]] = endpoint["id"]

    def _create_format_dict(self, client: HttpClient) -> Dict[str, Any]:
        """
        Crée un dictionnaire de formatage avec toutes les valeurs extraites disponibles.
        Ce dictionnaire pourra ètre utilisé pour formater des chaÃ®nes avec la syntaxe {variable}.
        """
        format_dict = {}

        # Ajouter les valeurs extraites par URL
        for url, val in client.results.items():
            # Essayer de récupérer l'ID associéà  l'URL pour un formatage plus intuitif
            if url in self.url_to_id_map:
                id_key = self.url_to_id_map[url].split("-")[-1]
                format_dict[id_key] = val
            # Aussi ajouter avec le chemin de l'URL (compatibilité)
            path = url.split("/")[-1]
            format_dict[path] = val

        # Ajouter aussi les valeurs par ID
        for id_key, val in client.results_by_id.items():
            # Ajouter l'ID complet sans modification
            format_dict[id_key] = val
            # Aussi ajouter la partie aprÃ¨s le dernier tiret pour compatibilité
            id_part = id_key.split("-")[-1]
            format_dict[id_part] = val

        # Extraire les clés session_id de faÃ§on dynamique
        # Chercher session_id dans tous les résultats disponibles
        for session_key, session_value in client.results_by_id.items():
            if "session" in session_key.lower():
                format_dict["session_id"] = session_value
                break

        return format_dict

    def _format_string_safely(
        self,
        template: str,
        format_dict: Dict[str, Any],
        thread_id: int,
        context: str = "",
    ) -> Optional[str]:
        """
        Formate une chaÃ®ne en toute sécurité, en vérifiant les variables manquantes.
        Retourne la chaÃ®ne formatée ou None si des variables sont manquantes.

        Args:
            template: ChaÃ®neà  formater avec des variables entre accolades {var}
            format_dict: Dictionnaire contenant les valeursà  utiliser pour le formatage
            thread_id: ID du thread actuel pour les messages d'erreur
            context: Contexte pour les messages d'erreur (ex: "ID", "paramÃ¨tre", "données")

        Returns:
            La chaÃ®ne formatée ou None si des variables sont manquantes
        """
        if not isinstance(template, str) or "{" not in template:
            return template

        # Vérifier si des variables requises sont manquantes
        missing_vars = self._find_missing_variables(template, format_dict)
        if missing_vars:
            print(
                f"Erreur critique: Variables requises manquantes dans {context}: {missing_vars}"
            )
            print(f"Variables disponibles: {list(format_dict.keys())}")
            print(f"Séquence terminée pour le thread {thread_id}")
            return None

        try:
            return template.format(**format_dict)
        except KeyError as e:
            print(
                f"Erreur critique: Variable requise manquante lors du formatage {context}: {e}"
            )
            print(f"Variables disponibles: {list(format_dict.keys())}")
            print(f"Séquence terminée pour le thread {thread_id}")
            return None

    def _find_missing_variables(
        self, template_string: str, available_vars: Dict[str, Any]
    ) -> List[str]:
        """
        Trouve les variables manquantes dans une chaÃ®ne de modÃ¨le.
        Retourne une liste des noms de variables manquantes.
        """
        if not isinstance(template_string, str) or "{" not in template_string:
            return []

        # Extraire toutes les variables requises de la chaÃ®ne de modÃ¨le
        # Motif pour capturer les noms de variables entre accolades: {variable_name}
        var_pattern = r"\{([^{}]+)\}"
        required_vars = re.findall(var_pattern, template_string)

        # Vérifier quelles variables sont manquantes
        missing = [var for var in required_vars if var not in available_vars]

        # Afficher plus d'informations en cas de variables manquantes
        if missing:
            print(f"Variables requises: {required_vars}")
            print(f"Variables disponibles: {list(available_vars.keys())}")

        return missing

    async def run_sequence(self, thread_id: int = 0):
        """Exécute une séquence complÃ¨te d'appels pour un thread."""
        try:
            # Passer l'option verify_ssl au client HTTP
            verify_ssl = self.config.get("verify_ssl", True)
            client = HttpClient(
                self.config["base_url"],
                self.config["jwt_token"],
                self.timer,
                verify_ssl=verify_ssl,
                config=self.config,
            )

            # Calculer le nombre total d'étapes pour ce thread
            total_steps = len(self.config["endpoints"])
            print(f"Thread {thread_id}: Démarrage - {total_steps} étapes à  exécuter")

            # Parcourir tous les endpoints définis dans la configuration
            for step, endpoint_config in enumerate(self.config["endpoints"], 1):
                await self._process_endpoint(
                    client, endpoint_config, thread_id, step, total_steps
                )

            print(
                f"Thread {thread_id}: Terminé - Toutes les {total_steps} étapes ont été exécutées"
            )

        except Exception as e:
            print(f"Erreur catastrophique dans le thread {thread_id}: {str(e)}")
            traceback.print_exc()

    async def _process_endpoint(
        self,
        client: HttpClient,
        endpoint_config: Dict[str, Any],
        thread_id: int,
        current_step: int,
        total_steps: int,
    ) -> None:
        """Traite un endpoint individuel."""
        try:
            # Préparer l'URL et l'ID de l'endpoint
            endpoint_url = endpoint_config["url"]
            endpoint_id = endpoint_config.get("id")
            endpoint_data = endpoint_config.get("data")

            # Traiter les dépendances si nécessaire
            if "depends_on" in endpoint_config and endpoint_config["depends_on"]:
                # Créer le dictionnaire de formatage avec toutes les valeurs extraites
                format_dict = self._create_format_dict(client)

                # Appliquer les formatages aux différents éléments de la requète
                result = self._prepare_request_with_dependencies(
                    endpoint_config, format_dict, thread_id, endpoint_url, endpoint_id
                )

                if result is None:
                    return  # Arrèter le traitement si une erreur critique survient

                endpoint_url, endpoint_id, endpoint_data = result

            # Préparer le chemin du fichier si nécessaire
            file_path = self._prepare_file_path(endpoint_config)
            form_name = endpoint_config.get("form_name", "file")

            # Exécuter la requète HTTP
            await self._execute_request(
                client,
                endpoint_url,
                endpoint_config,
                endpoint_data,
                endpoint_id,
                thread_id,
                file_path,
                form_name,
                current_step,
                total_steps,
            )

        except Exception as e:
            print(
                f"Erreur dans la séquence (Thread {thread_id}, étape {current_step}/{total_steps}, endpoint {endpoint_config.get('url')}): {str(e)}"
            )
            traceback.print_exc()
            # Continuer avec le prochain endpoint malgré l'erreur

    def _prepare_request_with_dependencies(
        self,
        endpoint_config: Dict[str, Any],
        format_dict: Dict[str, Any],
        thread_id: int,
        endpoint_url: str,
        endpoint_id: Optional[str],
    ) -> Optional[Tuple[str, Optional[str], Optional[Dict[str, Any]]]]:
        """
        Prépare la requète en appliquant les dépendances.
        Retourne un tuple (url, id, data) ou None en cas d'erreur.
        """
        # Formater l'ID de l'endpoint si nécessaire
        if endpoint_id and "{" in endpoint_id:
            formatted_id = self._format_string_safely(
                endpoint_id, format_dict, thread_id, "l'ID de l'endpoint"
            )
            if formatted_id is None:
                return None
            endpoint_id = formatted_id

        # Formater l'URL si nécessaire
        if "{" in endpoint_url:
            formatted_url = self._format_string_safely(
                endpoint_url, format_dict, thread_id, "l'URL de l'endpoint"
            )
            if formatted_url is None:
                return None
            endpoint_url = formatted_url

        # Traiter les paramÃ¨tres d'URL
        endpoint_url = self._process_url_params(
            endpoint_url, endpoint_config, format_dict, thread_id
        )
        if endpoint_url is None:
            return None

        # Préparer les données avec les dépendances
        endpoint_data = self._process_request_data(
            endpoint_config, format_dict, thread_id
        )
        if (
            endpoint_data is False
        ):  # False indique une erreur (None est une valeur valide pour data)
            return None

        return endpoint_url, endpoint_id, endpoint_data

    def _process_url_params(
        self,
        endpoint_url: str,
        endpoint_config: Dict[str, Any],
        format_dict: Dict[str, Any],
        thread_id: int,
    ) -> Optional[str]:
        """
        Traite les paramÃ¨tres d'URL et les ajoute à  l'URL.
        Retourne l'URL miseà  jour ou None en cas d'erreur.
        """
        params = endpoint_config.get("params", {})
        if not params:
            return endpoint_url

        # Copie pour éviter de modifier l'original
        params_copy = params.copy()

        # Remplace les modÃ¨les dans les paramÃ¨tres
        for param_key, param_value in params_copy.items():
            if isinstance(param_value, str) and "{" in param_value:
                formatted_value = self._format_string_safely(
                    param_value, format_dict, thread_id, f"paramÃ¨tre {param_key}"
                )
                if formatted_value is None:
                    return None
                params_copy[param_key] = formatted_value

        # Ajoute les paramÃ¨tresà  l'URL
        param_strings = [f"{key}={value}" for key, value in params_copy.items()]

        if param_strings:
            # Vérifie si l'URL contient déjÃ  des paramÃ¨tres
            separator = "&" if "?" in endpoint_url else "?"
            endpoint_url += separator + "&".join(param_strings)

        return endpoint_url

    def _process_request_data(
        self,
        endpoint_config: Dict[str, Any],
        format_dict: Dict[str, Any],
        thread_id: int,
    ) -> Union[Dict[str, Any], None, bool]:
        """
        Traite les données de la requète.
        Retourne les données formatées, None s'il n'y a pas de données, ou False en cas d'erreur.
        """
        if not endpoint_config.get("data"):
            return None

        # Copie les données pour éviter de modifier l'original
        endpoint_data = endpoint_config.get("data", {}).copy()

        # Copie du dictionnaire pour éviter de modifier l'original pendant l'itération
        endpoint_data_copy = endpoint_data.copy()

        # Remplace les modÃ¨les dans les données
        for data_key, data_value in endpoint_data_copy.items():
            if isinstance(data_value, str) and "{" in data_value:
                formatted_value = self._format_string_safely(
                    data_value, format_dict, thread_id, f"donnée {data_key}"
                )
                if formatted_value is None:
                    return False
                endpoint_data[data_key] = formatted_value

        return endpoint_data

    def _prepare_file_path(self, endpoint_config: Dict[str, Any]) -> Optional[str]:
        """Prépare le chemin du fichierà  télécharger si nécessaire."""
        if "file" not in endpoint_config:
            return None

        # Si un chemin de fichier relatif est fourni, le résoudre par rapport au dossier "upload"
        file_path = endpoint_config["file"]
        if not os.path.isabs(file_path):
            file_path = os.path.join("upload", file_path)

        return file_path

    async def _execute_request(
        self,
        client: HttpClient,
        endpoint_url: str,
        endpoint_config: Dict[str, Any],
        endpoint_data: Optional[Dict[str, Any]],
        endpoint_id: Optional[str],
        thread_id: int,
        file_path: Optional[str],
        form_name: str,
        current_step: int,
        total_steps: int,
    ) -> None:
        """Exécute la requète HTTP et gÃ¨re la réponse."""
        # Process headers if they contain variables
        headers = endpoint_config.get("headers")
        if headers and "depends_on" in endpoint_config:
            format_dict = self._create_format_dict(client)
            processed_headers = {}
            for key, value in headers.items():
                if isinstance(value, str) and "{" in value:
                    formatted_value = self._format_string_safely(
                        value, format_dict, thread_id, f"header {key}"
                    )
                    if formatted_value:
                        processed_headers[key] = formatted_value
                        # Debug output for Authorization header
                        if key == "Authorization" and "Bearer" in formatted_value:
                            token_preview = formatted_value[7:27] + "..." if len(formatted_value) > 27 else formatted_value[7:]
                            print(f"[DEBUG] Token injected for {endpoint_id or endpoint_url}: {token_preview}")
                    else:
                        processed_headers[key] = value
                else:
                    processed_headers[key] = value
            headers = processed_headers
        
        # Fait l'appel HTTP
        _response_data, continue_sequence = await client.make_request(
            endpoint=endpoint_url,
            method=endpoint_config.get("method", "GET"),
            headers=headers,
            data=endpoint_data,
            extract=endpoint_config.get("extract"),
            thread_id=thread_id,
            endpoint_id=endpoint_id,
            file_path=file_path,
            form_name=form_name,
            current_step=current_step,
            total_steps=total_steps,
        )

        # Si on doit terminer la séquence (ex: erreur d'authentification)
        if not continue_sequence:
            print(
                f"Thread {thread_id}: Séquence terminée prématurémentà  l'étape {current_step}/{total_steps}."
            )
            raise RuntimeError("Séquence terminée prématurément")


async def asyncio_process_test_cases(
    config_path: str, 
    test_type: str = "standard",
    skip_jwt_validation: bool = False,
    user_params: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Enhanced function for executing load tests with different test types.
    
    Args:
        config_path: Path to configuration file or context name
        test_type: Type of test to run (standard, stress, smoke, chaos)
        skip_jwt_validation: Whether to skip JWT token validation
        user_params: User-specified parameters to override test type defaults
        
    Returns:
        True if test completed successfully, False otherwise
    """
    try:
        # Load and merge configuration
        global_config_path, context_config_path = get_config_paths(config_path)
        
        print(f"[INFO] Loading configuration:")
        print(f"  Global: {global_config_path}")
        print(f"  Context: {context_config_path}")
        
        async with aiofiles.open(global_config_path, "r") as gf:
            global_content = await gf.read()
            global_config = json.loads(global_content)
            
        async with aiofiles.open(context_config_path, "r") as cf:
            context_content = await cf.read()
            context_config = json.loads(context_content)
        
        # Merge configurations
        config = global_config.copy()
        config.update(context_config)
        if "endpoints" in context_config:
            config["endpoints"] = context_config["endpoints"]
            
        # Store context file path for reporting
        config["_context_file"] = context_config_path
        
        # Validate JWT token if required
        if not skip_jwt_validation:
            print("\\n[INFO]  Validating JWT token...")
            jwt_valid, jwt_message = await validate_jwt(
                config["base_url"],
                config["jwt_token"], 
                config.get("verify_ssl", True)
            )
            
            print(jwt_message)
            
            if not jwt_valid:
                print("âŒ Cannot run tests with invalid JWT token. Check config.global.json")
                return False
                
        # Display configuration summary
        print(f"\n[CONFIG] Configuration Summary:")
        print(f"  Target URL: {config.get('base_url')}")
        print(f"  SSL Verification: {config.get('verify_ssl', True)}")
        print(f"  Endpoints: {len(config.get('endpoints', []))}")
        
        # Display timeout configuration
        print(f"  Timeouts:")
        timeout_config = config.get('timeout', {})
        print(f"    Connect: {timeout_config.get('connect', 10.0)}s")
        print(f"    Read: {timeout_config.get('read', 30.0)}s") 
        print(f"    Write: {timeout_config.get('write', 30.0)}s")
        print(f"    Pool: {timeout_config.get('pool', 10.0)}s")
        
        # Import here to avoid circular dependency
        from .runner import AsyncioTestRunner
        
        # Create and run test
        runner = AsyncioTestRunner(config, test_type)
        success = await runner.run_test(user_params)
        
        if success:
            print("\n[SUCCESS] Test completed successfully!")
        else:
            print("\n[ERROR] Test completed with errors!")
            
        return success
        
    except json.JSONDecodeError as e:
        print(f"âŒ Invalid JSON in configuration file: {str(e)}")
        return False
        
    except FileNotFoundError as e:
        print(f"âŒ Configuration file not found: {str(e)}")
        return False
        
    except Exception as e:
        print(f"âŒ Test execution failed: {str(e)}")
        traceback.print_exc()
        return False


async def validate_test_configuration(config_path: str) -> bool:
    """
    Validate test configuration without running the test.
    
    Args:
        config_path: Path to configuration file or context name
        
    Returns:
        True if configuration is valid
    """
    try:
        global_config_path, context_config_path = get_config_paths(config_path)
        
        # Load configurations
        async with aiofiles.open(global_config_path, "r") as gf:
            global_content = await gf.read()
            global_config = json.loads(global_content)
            
        async with aiofiles.open(context_config_path, "r") as cf:
            context_content = await cf.read()
            context_config = json.loads(context_content)
            
        # Merge and validate
        config = global_config.copy()
        config.update(context_config)
        
        # Check required fields
        required_fields = ["base_url", "jwt_token", "endpoints"]
        missing_fields = [field for field in required_fields if field not in config]
        
        if missing_fields:
            print(f"âŒ Missing required configuration fields: {missing_fields}")
            return False
            
        # Validate endpoints structure
        endpoints = config.get("endpoints", [])
        if not isinstance(endpoints, list) or len(endpoints) == 0:
            print("âŒ Configuration must contain at least one endpoint")
            return False
            
        for i, endpoint in enumerate(endpoints):
            if not isinstance(endpoint, dict):
                print(f"âŒ Endpoint {i+1} must be a dictionary")
                return False
                
            if "url" not in endpoint:
                print(f"âŒ Endpoint {i+1} missing 'url' field")
                return False
                
            if "method" not in endpoint:
                print(f"[WARNING] Endpoint {i+1} missing 'method' field, will default to GET")
                
        print("[OK] Configuration is valid!")
        return True
        
    except json.JSONDecodeError as e:
        print(f"âŒ Invalid JSON in configuration: {str(e)}")
        return False
        
    except Exception as e:
        print(f"âŒ Configuration validation failed: {str(e)}")
        return False


def get_available_test_types() -> Dict[str, str]:
    """
    Get available test types with descriptions.
    
    Returns:
        Dictionary mapping test type names to descriptions
    """
    return {
        "standard": "Balanced load testing with moderate concurrency and realistic user behavior",
        "stress": "High-intensity testing with aggressive patterns to find breaking points", 
        "smoke": "Light validation testing with minimal load to verify basic functionality",
        "chaos": "Unpredictable load patterns with random behaviors to test resilience"
    }


def print_test_type_help():

    """Print help information about available test types"""
    test_types = get_available_test_types()
    
    print("\n[INFO] Available Test Types:")
    print("=" * 60)
    
    for test_type, description in test_types.items():
        print(f"\\n{test_type.upper()}:")
        print(f"  {description}")
        
    print("\n[INFO] Usage Examples:")
    print("  # Standard load test")
    print("  python -m src.main -c config.json --mode asyncio --test-type standard")
    print("  ")  
    print("  # Stress test with custom parameters")
    print("  python -m src.main -c config.json --mode asyncio --test-type stress --users 100 --duration 600")
    print("  ")
    print("  # Smoke test for validation")
    print("  python -m src.main -c config.json --mode asyncio --test-type smoke")
    print("  ")
    print("  # Chaos test")
    print("  python -m src.main -c config.json --mode asyncio --test-type chaos")


async def process_test_cases(config_path: str, skip_jwt_validation: bool = False):
    """Fonction principale pour exécuter les tests de charge."""
    try:
        global_config_path, context_config_path = get_config_paths(config_path)

        # Ajout : afficher le fichier de contexte utilisé dans le rapport
        print(f"Fichier de contexte utilisé : {context_config_path}")

        # ...chargement des configs et fusion...
        async with aiofiles.open(global_config_path, "r") as gf:
            global_content = await gf.read()
            global_config = json.loads(global_content)
        async with aiofiles.open(context_config_path, "r") as cf:
            context_content = await cf.read()
            context_config = json.loads(context_content)

        config = global_config.copy()
        config.update(context_config)
        if "endpoints" in context_config:
            config["endpoints"] = context_config["endpoints"]

        # Ajout : stocker le chemin du contexte dans la config pour le rapport
        config["_context_file"] = context_config_path

        # Valider le jeton JWT avant de démarrer les tests
        if not skip_jwt_validation:
            print("Validation du jeton JWT...")
            jwt_valid, jwt_message = await validate_jwt(
                config["base_url"], config["jwt_token"], config.get("verify_ssl", True)
            )

            print(jwt_message)

            if not jwt_valid:
                print(
                    "Les tests ne peuvent pas ètre exécutés avec un jeton JWT invalide, voir fichier config.global.json."
                )
                return

        # Afficher la configuration des timeouts
        print(f"Configuration des timeouts:")
        print(f"  - Connexion: {config.get('timeout_connect', 10.0)}s")
        print(f"  - Lecture: {config.get('timeout_read', 30.0)}s")
        print(f"  - écriture: {config.get('timeout_write', 30.0)}s")
        print(f"  - Pool: {config.get('timeout_pool', 10.0)}s")

        timer = Timer()
        timer.start_global()

        # Configurer les informations sur les threads et tÃ¢ches
        num_threads = config.get("num_threads", 1)
        num_tasks_per_thread = len(config.get("endpoints", []))
        timer.set_thread_and_task_info(num_threads, num_tasks_per_thread)

        # Crée et exécute les threads en parallÃ¨le
        tasks = []
        for i in range(num_threads):
            test_sequence = TestSequence(config, timer)
            task = asyncio.create_task(test_sequence.run_sequence(thread_id=i))
            tasks.append(task)

        # Attend que tous les threads terminent
        await asyncio.gather(*tasks)

        timer.end_global()
        await timer.print_summary(
            context_file=config.get("_context_file"), base_url=config.get("base_url")
        )
    except json.JSONDecodeError as e:
        print(f"Erreur: Le fichier de configuration n'est pas un JSON valide: {str(e)}")
    except FileNotFoundError:
        print(f"Erreur: Le fichier de configuration '{config_path}' est introuvable")
    except Exception as e:
        print(f"Erreur lors de l'exécution des tests: {str(e)}")
        traceback.print_exc()